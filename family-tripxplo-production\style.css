/* Google Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--black);
  background-color: var(--white);
}

.family-emi-website {
  min-height: 100vh;
  background: var(--white);
  position: relative;
  overflow-x: hidden;
}

/* Navigation */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.75rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px; /* Fixed navbar height */
}

.logo {
  font-size: 1.5rem;
  font-weight: 800;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  height: 100%; /* Take full navbar height */
}

.logo-image {
  height: 50px; /* Reduced from 100px to fit in navbar */
  width: auto;
  max-width: 200px; /* Reduced max-width proportionally */
  object-fit: contain; /* Ensure proper scaling */
}

.logo-trip {
  color: var(--green);
}

.logo-xplo {
  color: var(--x-1st);
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--black);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--primary);
}

/* Authentication Button */
.auth-btn {
  background: linear-gradient(135deg, var(--primary), var(--green));
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 10px rgba(40, 167, 69, 0.2);
  margin-left: 1rem;
}

.auth-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.auth-btn i {
  font-size: 0.9rem;
}

/* Authentication Modal */
.auth-modal {
  max-width: 450px;
  width: 90%;
  border-radius: 16px;
  overflow: hidden;
}

.auth-modal .modal-header h3 {
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-form-intro {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-form-intro p {
  color: var(--gray-600);
  font-size: 1rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.user-display {
  background: var(--gray-50);
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
  border: 1px solid var(--gray-200);
}

.user-display i {
  color: var(--primary);
  font-size: 1.2rem;
}

.user-display span {
  font-weight: 600;
  color: var(--black);
}

.change-details-btn {
  background: none;
  border: none;
  color: var(--primary);
  cursor: pointer;
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.change-details-btn:hover {
  background: var(--primary-light);
}

/* Auth Switch Links */
.auth-switch {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200);
}

.auth-switch p {
  margin: 0;
  color: var(--gray-600);
  font-size: 0.9rem;
}

.switch-btn {
  background: none;
  border: none;
  color: var(--primary);
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
  font-size: 0.9rem;
  padding: 0;
  transition: color 0.3s ease;
}

.switch-btn:hover {
  color: var(--primary-dark);
}

.mobile-input-container {
  display: flex;
  align-items: center;
  border: 2px solid var(--gray-300);
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.3s ease;
}

.mobile-input-container:focus-within {
  border-color: var(--primary);
}

.country-code {
  background: var(--gray-100);
  padding: 0.75rem 1rem;
  font-weight: 600;
  color: var(--gray-700);
  border-right: 1px solid var(--gray-300);
}

.mobile-input {
  border: none !important;
  flex: 1;
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

.mobile-input:focus {
  outline: none;
}

.pin-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.pin-input {
  font-size: 1.5rem;
  letter-spacing: 0.5rem;
  text-align: center;
  padding: 1rem;
  border: 2px solid var(--gray-300);
  border-radius: 8px;
  width: 100%;
  transition: border-color 0.3s ease;
}

.pin-input:focus {
  border-color: var(--primary);
  outline: none;
}

.toggle-pin-visibility {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.toggle-pin-visibility:hover {
  color: var(--primary);
}

.new-user-setup {
  background: var(--green-light);
  border: 1px solid var(--green);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.setup-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--green-dark);
}

.setup-message i {
  color: var(--green);
  font-size: 1.2rem;
}

.setup-message p {
  margin: 0;
  font-weight: 500;
}

.auth-loading {
  text-align: center;
  padding: 3rem 2rem;
}

.auth-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.auth-loading p {
  color: var(--gray-600);
  font-size: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* User Menu Dropdown */
.user-menu-dropdown {
  background: white;
  border-radius: 16px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--gray-200);
  min-width: 280px;
  overflow: hidden;
  animation: fadeInDown 0.3s ease;
}

.user-menu-header {
  background: linear-gradient(135deg, var(--primary), var(--green));
  color: white;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar i {
  font-size: 2.5rem;
  opacity: 0.9;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.user-contact {
  font-size: 0.85rem;
  opacity: 0.9;
  margin-bottom: 0.1rem;
}

.user-mobile {
  font-size: 0.8rem;
  opacity: 0.8;
}

.user-menu-items {
  padding: 0.5rem 0;
}

.user-menu-item {
  width: 100%;
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--gray-700);
  transition: all 0.2s ease;
  position: relative;
}

.user-menu-item:hover {
  background: var(--gray-50);
  color: var(--primary);
  transform: translateX(2px);
}

.user-menu-item i {
  width: 20px;
  text-align: center;
  color: var(--gray-500);
  font-size: 1.1rem;
}

.user-menu-item:hover i {
  color: var(--primary);
}

.menu-item-content {
  flex: 1;
}

.menu-item-title {
  display: block;
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 0.1rem;
}

.menu-item-desc {
  display: block;
  font-size: 0.8rem;
  color: var(--gray-500);
  opacity: 0.8;
}

.user-menu-item:hover .menu-item-desc {
  color: var(--primary);
  opacity: 0.7;
}

.menu-divider {
  height: 1px;
  background: var(--gray-200);
  margin: 0.5rem 0;
}

.logout-item {
  color: #e74c3c !important;
}

.logout-item:hover {
  background: #fdf2f2 !important;
  color: #c0392b !important;
}

.logout-item:hover i {
  color: #c0392b !important;
}

.logout-item:hover .menu-item-desc {
  color: #c0392b !important;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Error Popup */
.error-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  animation: fadeIn 0.3s ease;
}

.error-popup-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: slideInUp 0.3s ease;
}

.error-icon {
  color: #e74c3c;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-message {
  color: var(--gray-700);
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.error-close-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.error-close-btn:hover {
  background: #c0392b;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Profile Modal */
.profile-modal {
  max-width: 600px;
  width: 90%;
}

.profile-section {
  text-align: center;
}

.profile-avatar {
  position: relative;
  display: inline-block;
  margin-bottom: 2rem;
}

.profile-avatar i {
  font-size: 5rem;
  color: var(--primary);
}

.change-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.change-avatar-btn:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

.profile-form {
  text-align: left;
}

.form-help {
  color: var(--gray-500);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: block;
}

.profile-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
  padding: 1.5rem;
  background: var(--gray-50);
  border-radius: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-align: center;
}

.stat-item i {
  font-size: 1.5rem;
  color: var(--primary);
}

.stat-content {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--black);
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--gray-600);
  margin-top: 0.25rem;
}

.secondary-btn {
  background: var(--gray-200);
  color: var(--gray-700);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.secondary-btn:hover {
  background: var(--gray-300);
  color: var(--gray-800);
}

/* Booking Modal */
.booking-modal {
  max-width: 900px;
  width: 95%;
  max-height: 85vh;
  overflow-y: auto;
}

.loading-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--gray-600);
}

.loading-state .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.error-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--gray-600);
}

.error-state i {
  font-size: 3rem;
  color: #e74c3c;
  margin-bottom: 1rem;
}

.error-state h4 {
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.retry-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.retry-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Bookings List */
.bookings-list {
  max-height: 70vh;
  overflow-y: auto;
}

.bookings-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--gray-200);
}

.bookings-header h4 {
  color: var(--gray-700);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bookings-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--gray-600);
}

.empty-state i {
  font-size: 4rem;
  color: var(--gray-400);
  margin-bottom: 1rem;
}

.empty-state h4 {
  font-size: 1.5rem;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.empty-state p {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 2rem;
}

.cta-btn {
  background: linear-gradient(135deg, var(--primary), var(--green));
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Booking Cards */
.booking-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.booking-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gray-200);
}

.booking-reference h5 {
  margin: 0 0 0.5rem 0;
  color: var(--gray-800);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.booking-date {
  text-align: right;
  color: var(--gray-500);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-active {
  background: #e3f2fd;
  color: #1976d2;
}

.status-completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-cancelled {
  background: #ffebee;
  color: #d32f2f;
}

.booking-details {
  margin-bottom: 1.5rem;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section h6 {
  margin: 0 0 0.75rem 0;
  color: var(--gray-700);
  font-size: 0.95rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--gray-100);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  flex-direction: row !important;
  align-items: center !important;
  gap: 2px !important;
  justify-content: flex-start;
}

.detail-item i {
  width: auto !important;
  min-width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  vertical-align: middle;
}

.detail-label {
  color: var(--gray-600);
  font-size: 0.9rem;
  font-weight: 500;
}

.detail-value {
  color: var(--gray-800);
  font-weight: 600;
  text-align: right;
}

.detail-value.amount {
  color: var(--primary);
  font-weight: 700;
}

/* Payment Status */
.payment-status {
  background: var(--gray-50);
  padding: 1rem;
  border-radius: 8px;
}

.payment-progress {
  margin-bottom: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--green));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: var(--gray-600);
}

.next-due {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gray-700);
  font-weight: 600;
  font-size: 0.9rem;
}

.next-due i {
  color: var(--primary);
}

/* Booking Actions */
.booking-actions {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200);
}

.action-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.action-btn.primary {
  background: var(--primary);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: var(--gray-200);
  color: var(--gray-700);
}

.action-btn.secondary:hover {
  background: var(--gray-300);
  color: var(--gray-800);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10002;
  max-width: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid;
}

.notification-success .notification-content {
  border-left-color: #28a745;
  color: #155724;
}

.notification-error .notification-content {
  border-left-color: #dc3545;
  color: #721c24;
}

.notification-warning .notification-content {
  border-left-color: #ffc107;
  color: #856404;
}

.notification-info .notification-content {
  border-left-color: #17a2b8;
  color: #0c5460;
}

.notification-close {
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  margin-left: auto;
  padding: 0.25rem;
}

.notification-close:hover {
  opacity: 1;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Enhanced Profile Modal */
.profile-modal-enhanced {
  max-width: 480px;
  width: 85%;
  max-height: 70vh;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.profile-header-section {
  position: relative;
  background: linear-gradient(135deg, var(--primary), var(--green));
  color: white;
  padding: 1rem;
  overflow: hidden;
}

.profile-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.profile-header-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.profile-avatar-container {
  position: relative;
}

.profile-avatar-large {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.avatar-status-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 30px;
  height: 30px;
  background: #ffd700;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 0.8rem;
  border: 2px solid white;
}

.profile-header-info h2 {
  margin: 0 0 0.25rem 0;
  font-size: 1.2rem;
  font-weight: 700;
}

.profile-header-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.8rem;
}

.profile-member-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.profile-body-enhanced {
  padding: 1rem;
  max-height: 50vh;
  overflow-y: auto;
}

/* Profile Stats Section */
.profile-stats-section {
  margin-bottom: 2rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--gray-700);
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--gray-100);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.trips {
  border-left: 4px solid #3498db;
}

.stat-card.spending {
  border-left: 4px solid #e74c3c;
}

.stat-card.savings {
  border-left: 4px solid #2ecc71;
}

.stat-card .stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.stat-card.trips .stat-icon {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.stat-card.spending .stat-icon {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.stat-card.savings .stat-icon {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--gray-800);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--gray-600);
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: #2ecc71;
  margin-top: 0.5rem;
}

/* Enhanced Form Styles */
.profile-form-section {
  margin-bottom: 1.5rem;
}

.profile-form-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.form-group-enhanced {
  position: relative;
}

.form-label-enhanced {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.95rem;
}

.input-wrapper {
  position: relative;
}

.form-input-enhanced {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--gray-200);
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: white;
  color: var(--gray-800);
}

.form-input-enhanced:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.input-focus-border {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.form-input-enhanced:focus + .input-focus-border {
  transform: scaleX(1);
}

.mobile-input-enhanced {
  display: flex;
  align-items: center;
  border: 2px solid var(--gray-200);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.mobile-input-enhanced:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.country-code-enhanced {
  background: var(--gray-50);
  padding: 0.75rem 1rem;
  border-right: 1px solid var(--gray-200);
  font-weight: 600;
  color: var(--gray-600);
}

.mobile-input-enhanced .form-input-enhanced {
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.form-help-enhanced {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: var(--gray-500);
}

/* Security Section */
.security-section {
  margin-bottom: 2rem;
}

.security-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.security-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--gray-50);
  border-radius: 12px;
  border: 1px solid var(--gray-200);
}

.security-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  background: var(--gray-200);
  color: var(--gray-600);
}

.security-icon.verified {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.security-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.security-title {
  font-weight: 600;
  color: var(--gray-800);
}

.security-desc {
  font-size: 0.9rem;
  color: var(--gray-600);
}

.security-status .status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-badge.verified {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

/* Quick Actions Section */
.quick-actions-section {
  margin-bottom: 1rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.75rem;
  background: white;
  border: 2px solid var(--gray-200);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--gray-700);
}

.quick-action-btn:hover {
  border-color: var(--primary);
  background: var(--gray-50);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-action-btn i {
  font-size: 1.2rem;
  color: var(--primary);
}

.quick-action-btn span {
  font-weight: 600;
  font-size: 0.8rem;
}

/* Enhanced Footer */
.profile-footer-enhanced {
  padding: 1rem 1.5rem;
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.btn-secondary-enhanced,
.btn-primary-enhanced {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 0.9rem;
}

.btn-secondary-enhanced {
  background: white;
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
}

.btn-secondary-enhanced:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.btn-primary-enhanced {
  background: var(--primary);
  color: white;
  border: 2px solid var(--primary);
}

.btn-primary-enhanced:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* Enhanced Booking Modal */
.booking-modal-enhanced {
  max-width: 1000px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.booking-header-section {
  position: relative;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 2rem;
  overflow: hidden;
}

.booking-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="bookingPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23bookingPattern)"/></svg>');
}

.booking-header-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.booking-icon {
  width: 70px;
  height: 70px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  backdrop-filter: blur(10px);
}

.booking-header-info h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.booking-header-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.booking-body-enhanced {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* Enhanced Loading State */
.loading-state-enhanced {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-animation {
  position: relative;
  margin-bottom: 2rem;
}

.loading-spinner-enhanced {
  width: 60px;
  height: 60px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  background: var(--primary);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-state-enhanced h4 {
  margin: 0 0 0.5rem 0;
  color: var(--gray-700);
  font-size: 1.2rem;
}

.loading-state-enhanced p {
  margin: 0;
  color: var(--gray-500);
}

/* Enhanced Empty State */
.empty-state-enhanced {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-state-animation {
  position: relative;
  margin-bottom: 2rem;
}

.empty-state-icon {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: var(--gray-400);
  margin: 0 auto 1rem;
  position: relative;
  overflow: hidden;
}

.empty-state-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.empty-state-particles span {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

.empty-state-particles span:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.empty-state-particles span:nth-child(2) {
  top: 30%;
  right: 20%;
  animation-delay: 1s;
}

.empty-state-particles span:nth-child(3) {
  bottom: 20%;
  left: 30%;
  animation-delay: 2s;
}

.empty-state-particles span:nth-child(4) {
  bottom: 30%;
  right: 30%;
  animation-delay: 1.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.empty-state-enhanced h3 {
  margin: 0 0 1rem 0;
  color: var(--gray-700);
  font-size: 1.5rem;
}

.empty-state-enhanced p {
  margin: 0 0 2rem 0;
  color: var(--gray-500);
  font-size: 1.1rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.empty-state-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.cta-btn-enhanced {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
  text-decoration: none;
}

.cta-btn-enhanced.primary {
  background: var(--primary);
  color: white;
}

.cta-btn-enhanced.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.cta-btn-enhanced.secondary {
  background: white;
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
}

.cta-btn-enhanced.secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.empty-state-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gray-600);
  font-size: 0.9rem;
}

.feature-item i {
  color: var(--primary);
}

/* Enhanced Booking List */
.bookings-list-enhanced {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.bookings-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--gray-200);
}

.bookings-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.bookings-title-section h3 {
  margin: 0;
  color: var(--gray-800);
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bookings-count {
  background: var(--primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.bookings-filters {
  display: flex;
  gap: 0.5rem;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: white;
  border: 2px solid var(--gray-200);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--gray-600);
}

.filter-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
}

.filter-btn.active {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
}

.bookings-container-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Enhanced Booking Cards */
.booking-card-enhanced {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.booking-card-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.booking-card-header {
  background: linear-gradient(135deg, var(--gray-50), white);
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.destination-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.destination-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary), var(--green));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.destination-info h4 {
  margin: 0 0 0.25rem 0;
  color: var(--gray-800);
  font-size: 1.3rem;
  font-weight: 700;
}

.package-name {
  margin: 0;
  color: var(--gray-600);
  font-size: 0.95rem;
}

.booking-status-section {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.booking-id {
  font-size: 0.85rem;
  color: var(--gray-500);
  font-family: 'Courier New', monospace;
}

.booking-card-body {
  padding: 1.5rem;
}

.booking-timeline {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
}

.booking-timeline::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 25px;
  right: 25px;
  height: 2px;
  background: var(--gray-200);
  z-index: 1;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 2;
}

.timeline-icon {
  width: 40px;
  height: 40px;
  background: white;
  border: 3px solid var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1rem;
}

.timeline-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.timeline-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--gray-700);
}

.timeline-date {
  font-size: 0.8rem;
  color: var(--gray-500);
}

/* Payment Summary Section */
.payment-summary-section {
  background: var(--gray-50);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.payment-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.payment-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid var(--gray-200);
}

.payment-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}

.payment-icon.advance {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.payment-icon.emi {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.payment-icon.total {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.payment-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.payment-label {
  font-size: 0.85rem;
  color: var(--gray-600);
  font-weight: 500;
}

.payment-amount {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--gray-800);
}

/* Enhanced Progress Bar */
.payment-progress-enhanced {
  background: white;
  border-radius: 10px;
  padding: 1rem;
  border: 1px solid var(--gray-200);
  margin-bottom: 1rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.progress-title {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.95rem;
}

.progress-percentage {
  font-weight: 700;
  color: var(--primary);
  font-size: 1rem;
}

.progress-bar-enhanced {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.progress-fill-enhanced {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--green));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.progress-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.progress-item.paid .progress-dot {
  background: var(--primary);
}

.progress-item.pending .progress-dot {
  background: var(--gray-400);
}

/* Next Payment Alert */
.next-payment-alert {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border: 1px solid #ffc107;
  border-radius: 10px;
  padding: 1rem;
}

.alert-icon {
  width: 40px;
  height: 40px;
  background: #ffc107;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
}

.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.alert-title {
  font-weight: 600;
  color: #856404;
  font-size: 0.95rem;
}

.alert-date {
  font-size: 0.85rem;
  color: #6c5ce7;
  font-weight: 500;
}

.alert-amount {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2d3436;
}

/* Booking Card Footer */
.booking-card-footer {
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  padding: 1.5rem;
}

.booking-actions-enhanced {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.action-btn-enhanced {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 0.9rem;
  text-decoration: none;
  flex: 1;
  justify-content: center;
}

.action-btn-enhanced.primary {
  background: var(--primary);
  color: white;
}

.action-btn-enhanced.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.action-btn-enhanced.payment {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn-enhanced.payment:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.action-btn-enhanced.secondary {
  background: white;
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
}

.action-btn-enhanced.secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.customer-info-footer {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200);
}

.customer-avatar {
  width: 40px;
  height: 40px;
  background: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.customer-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customer-name {
  font-weight: 600;
  color: var(--gray-800);
  font-size: 0.95rem;
}

.customer-contact {
  font-size: 0.85rem;
  color: var(--gray-500);
}

/* Mobile Responsive Enhancements */
@media (max-width: 768px) {
  .profile-modal-enhanced,
  .booking-modal-enhanced {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }

  .profile-header-section,
  .booking-header-section {
    padding: 1.5rem 1rem;
  }

  .profile-header-content,
  .booking-header-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .profile-body-enhanced,
  .booking-body-enhanced {
    padding: 1rem;
    max-height: calc(100vh - 200px);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .bookings-header-enhanced {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .bookings-filters {
    width: 100%;
    justify-content: space-between;
  }

  .filter-btn {
    flex: 1;
    justify-content: center;
  }

  .booking-card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .booking-status-section {
    align-items: flex-start;
    text-align: left;
  }

  .booking-timeline {
    flex-direction: column;
    gap: 1rem;
  }

  .booking-timeline::before {
    display: none;
  }

  .timeline-item {
    flex-direction: row;
    text-align: left;
  }

  .payment-overview {
    grid-template-columns: 1fr;
  }

  .booking-actions-enhanced {
    flex-direction: column;
  }

  .progress-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .empty-state-actions {
    flex-direction: column;
    align-items: center;
  }

  .empty-state-features {
    flex-direction: column;
    gap: 1rem;
  }

  .profile-footer-enhanced {
    padding: 1rem;
    flex-direction: column-reverse;
    gap: 0.75rem;
  }

  .btn-secondary-enhanced,
  .btn-primary-enhanced {
    width: 100%;
    justify-content: center;
  }
}

/* Enhanced Booking Cards */
.booking-card-enhanced {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.booking-card-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: var(--primary);
}

/* Card Header */
.booking-card-header-enhanced {
  background: linear-gradient(135deg, var(--primary), #2ecc71);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.booking-reference-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.reference-id {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 700;
}

.reference-id i {
  font-size: 1.2rem;
}

.reference-text {
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.booking-date-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Status Badge Enhanced */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Card Body */
.booking-card-body-enhanced {
  padding: 2rem;
}

/* Information Sections */
.info-section {
  margin-bottom: 2rem;
  border: 1px solid var(--gray-100);
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.section-header {
  background: linear-gradient(135deg, var(--primary), #2ecc71);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
}

.section-header.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
  justify-content: space-between;
}

.section-header.clickable:hover {
  background: linear-gradient(135deg, #218838, #27ae60);
}

.section-header h6 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.section-header i {
  font-size: 1.1rem;
}

.toggle-icon {
  transition: transform 0.3s ease;
  font-size: 0.9rem;
}

.toggle-icon.rotated {
  transform: rotate(180deg);
}

/* Information Grid */
.info-grid {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--gray-100);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: var(--gray-600);
  font-weight: 500;
  font-size: 0.95rem;
}

.info-value {
  color: var(--gray-800);
  font-weight: 600;
  text-align: right;
  font-size: 0.95rem;
}

.info-value.amount {
  color: var(--primary);
  font-weight: 700;
  font-size: 1.05rem;
}

/* Payment Status Dropdown */
.payment-status-section {
  margin-bottom: 0;
}

.payment-status-dropdown {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: var(--gray-50);
}

.payment-status-dropdown.open {
  max-height: 500px;
}

.payment-progress-enhanced {
  padding: 1.5rem;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid var(--gray-200);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.progress-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: white;
}

.progress-icon.paid {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.progress-icon.pending {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.progress-icon.due {
  background: linear-gradient(135deg, var(--primary), #2ecc71);
}

.progress-icon.months-paid {
  background: linear-gradient(135deg, #17a2b8, #20c997);
}

.progress-icon.months-remaining {
  background: linear-gradient(135deg, #6f42c1, #e83e8c);
}

.progress-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.progress-label {
  font-size: 0.9rem;
  color: var(--gray-600);
  font-weight: 500;
}

.progress-amount {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--gray-800);
}

/* Progress Bar Container */
.progress-bar-container {
  background: white;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid var(--gray-200);
}

.progress-bar-enhanced {
  width: 100%;
  height: 12px;
  background: var(--gray-200);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 0.75rem;
  position: relative;
}

.progress-fill-enhanced {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), #2ecc71);
  border-radius: 6px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-percentage {
  text-align: center;
  font-weight: 600;
  color: var(--primary);
  font-size: 0.95rem;
}

/* Mobile Responsive for Enhanced Cards */
@media (max-width: 768px) {
  .booking-card-header-enhanced {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .booking-reference-section {
    flex-direction: column;
    gap: 0.75rem;
  }

  .booking-card-body-enhanced {
    padding: 1rem;
  }

  .info-grid {
    padding: 1rem;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    text-align: left;
  }

  .info-value {
    text-align: left;
  }

  .progress-info {
    gap: 0.75rem;
  }

  .progress-item {
    padding: 0.75rem;
  }

  .section-header {
    padding: 0.75rem 1rem;
  }
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 6rem 2rem 4rem; /* Reduced top padding since navbar is smaller */
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 800px;
  width: 100%;
  text-align: center;
  z-index: 10;
}

.hero-text {
  margin-bottom: 3rem;
}

.hero-title {
  font-size: 3.8rem;
  font-weight: 800;
  background: linear-gradient(272deg, #000000 0%, #000000 50%, #000000 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  animation: fadeInUp 1s ease-out;
  position: relative;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 5px;
  background: linear-gradient(272deg, var(--primary) 0%, #059669 50%, #3dbb74 100%);
  border-radius: 3px;
  animation: expandLine 1.5s ease-out 0.5s both;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.hero-subtitle {
  font-size: 1rem;
  color: #475569;
  margin-bottom: 2.5rem;
  line-height: 1.5;
  animation: fadeInUp 1s ease-out 0.3s both;
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.hero-subtitle::before {
  content: '\f005'; /* Font Awesome star icon */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: var(--primary);
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

.hero-subtitle::after {
  content: '\f005'; /* Font Awesome star icon */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: var(--primary);
  font-size: 1.2rem;
  animation: pulse 2s infinite 1s; /* Delayed animation */
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandLine {
  from {
    width: 0;
  }
  to {
    width: 120px;
  }
}

/* Search Form */
.search-form-container {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  margin-bottom: 1rem;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 0.5rem;
}

.input-group {
  position: relative;
}

.family-type-info-btn {
  text-align: center;
  padding: 0.3rem;
  background: rgba(21, 171, 139, 0.3);
  border-radius: 12px;
  cursor: pointer;
  border: 1px solid #e5e7eb;
}

.input-label {
  display: block;
  font-weight: 600;
  color: var(--text-clr);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input-label i {
  margin-right: 0.5rem;
  color: var(--primary);
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(21, 171, 139, 0.1);
}

/* Traveler Input Wrapper */
.traveler-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.traveler-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  text-align: left;
  flex: 1;
}

/* Family Type Info Button - Positioned next to traveler selector */
.family-type-info-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.family-type-info-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.family-type-info-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.family-type-info-btn:hover::before {
  opacity: 1;
}

.family-type-info-btn:active {
  transform: translateY(0) scale(1);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.family-type-info-btn i {
  font-size: 0.9rem;
  animation: pulse-info 2s infinite;
  z-index: 1;
}

@keyframes pulse-info {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

/* Family Type Display */
.family-type-display {
  margin-top: 1rem;
  text-align: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.family-type-label {
  font-weight: 600;
  color: var(--text-clr);
  margin-right: 0.5rem;
}

.family-type-name {
  font-weight: 700;
  color: var(--primary);
}

/* Compact Family Type Info Modal */
.family-type-info-modal {
  max-width: 600px;
  width: 85%;
  max-height: 70vh;
  overflow-y: auto;
  scroll-behavior: smooth;
  position: relative;
}

/* Global Enhanced Scrollbar Styles */
.enhanced-scroll,
.family-type-info-modal,
.modal-content,
.table-responsive,
.destination-suggestions,
.card-preview-body {
  scroll-behavior: smooth;
}

/* Utility class for applying enhanced scroll to any element */
.apply-enhanced-scroll {
  scroll-behavior: smooth;
}

.apply-enhanced-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.apply-enhanced-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  margin: 8px 0;
}

.apply-enhanced-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.apply-enhanced-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: scaleX(1.2);
}

.apply-enhanced-scroll::-webkit-scrollbar,
.family-type-info-modal::-webkit-scrollbar,
.modal-content::-webkit-scrollbar,
.table-responsive::-webkit-scrollbar,
.destination-suggestions::-webkit-scrollbar,
.card-preview-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.apply-enhanced-scroll::-webkit-scrollbar-track,
.family-type-info-modal::-webkit-scrollbar-track,
.modal-content::-webkit-scrollbar-track,
.table-responsive::-webkit-scrollbar-track,
.destination-suggestions::-webkit-scrollbar-track,
.card-preview-body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  margin: 8px 0;
}

.apply-enhanced-scroll::-webkit-scrollbar-thumb,
.family-type-info-modal::-webkit-scrollbar-thumb,
.modal-content::-webkit-scrollbar-thumb,
.table-responsive::-webkit-scrollbar-thumb,
.destination-suggestions::-webkit-scrollbar-thumb,
.card-preview-body::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.apply-enhanced-scroll::-webkit-scrollbar-thumb:hover,
.family-type-info-modal::-webkit-scrollbar-thumb:hover,
.modal-content::-webkit-scrollbar-thumb:hover,
.table-responsive::-webkit-scrollbar-thumb:hover,
.destination-suggestions::-webkit-scrollbar-thumb:hover,
.card-preview-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: scaleX(1.2);
}

/* Corner styling for elements with both horizontal and vertical scrollbars */
.apply-enhanced-scroll::-webkit-scrollbar-corner,
.table-responsive::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.05);
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.family-type-info-modal:hover .scroll-indicator {
  opacity: 0.6;
}

.scroll-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #10b981;
  transition: all 0.3s ease;
}

.scroll-dot.active {
  background: #059669;
  transform: scale(1.5);
}

/* Scroll Shadow Effects */
.family-type-info-modal::before,
.family-type-info-modal::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 20px;
  pointer-events: none;
  z-index: 5;
  transition: opacity 0.3s ease;
}

.family-type-info-modal::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), transparent);
  opacity: 0;
}

.family-type-info-modal::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.9), transparent);
  opacity: 0;
}

.family-type-info-modal.has-scroll-top::before {
  opacity: 1;
}

.family-type-info-modal.has-scroll-bottom::after {
  opacity: 1;
}

.family-type-info-content {
  padding: 0.25rem 0;
}

.info-description {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 6px;
  border-left: 3px solid var(--primary);
}

.info-description p {
  margin: 0;
  color: #64748b;
  font-size: 0.75rem;
  line-height: 1.3;
}

/* Compact Loading State */
.family-type-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  color: #64748b;
  font-size: 0.8rem;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 0.75rem;
}

/* Compact Table Styles */
.family-type-table-container {
  padding-left: 0.5rem;
}

.table-responsive {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  scroll-behavior: smooth;
}

.family-type-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  font-size: 0.7rem;
}

.family-type-table thead {
  background: linear-gradient(#62bb93);
  color: white;
}

.family-type-table th,
.family-type-table td {
  padding: 0.35rem 0.3rem;
  border-bottom: 1px solid #e2e8f0;
}

.family-type-table th {
  font-weight: 600;
  font-size: 0.6rem;
  text-align: left;
  letter-spacing: 0.03em;
}

.family-type-table tbody tr:hover {
  background-color: #f8fafc;
}

.family-type-table tbody tr:nth-child(even) {
  background-color: #f9fafb;
}

.family-type-table tbody tr:nth-child(even):hover {
  background-color: #f1f5f9;
}

/* Compact Table Cell Styling */
.family-type-info-modal .family-type-name {
  font-weight: 700;
  color: #3dbb74;
  font-size: 0.6rem;
}

.count-cell {
  text-align: center;
  font-weight: 500;
  color: #475569;
  font-size: 0.65rem;
}
/* Compact Error State */
.family-type-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  text-align: center;
}

.error-icon {
  font-size: 2rem;
  color: #ef4444;
  margin-bottom: 0.75rem;
}

.family-type-error h4 {
  margin: 0 0 0.375rem 0;
  color: #1e293b;
  font-size: 0.9rem;
}

.family-type-error p {
  margin: 0 0 1rem 0;
  color: #64748b;
  max-width: 350px;
  font-size: 0.8rem;
}

.retry-btn {
  padding: 0.5rem 1rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.retry-btn:hover {
  background: #059669;
  transform: translateY(-2px);
}

.close-btn {
  padding: 0.5rem 1.25rem;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #4b5563;
}

/* Responsive Design for Traveler Input */
@media (max-width: 768px) {
  .family-type-display {
    padding: 0.5rem;
    margin-top: 0.75rem;
  }

  .family-type-label {
    font-size: 0.85rem;
  }

  .family-type-name {
    font-size: 0.9rem;
  }

  .traveler-input-wrapper {
    gap: 0.375rem;
  }

  .family-type-info-btn {
    width: 36px;
    height: 36px;
  }

  .family-type-info-btn i {
    font-size: 0.85rem;
  }

  /* Compact Family Type Modal on Tablet */
  .family-type-info-modal {
    width: 95%;
    max-height: 75vh;
    margin: 0.25rem;
  }

  /* Adjust scroll indicator for tablet */
  .scroll-indicator {
    right: 8px;
  }

  .scroll-dot {
    width: 3px;
    height: 3px;
  }

  .family-type-table {
    font-size: 0.65rem;
  }

  .family-type-table th,
  .family-type-table td {
    padding: 0.3rem 0.25rem;
  }

  .family-type-table th {
    font-size: 0.6rem;
  }

  .info-description {
    padding: 0.4rem;
    margin-bottom: 0.4rem;
  }

  .info-description p {
    font-size: 0.7rem;
  }

  .family-type-info-modal .family-type-name {
    font-size: 0.55rem;
  }

  .count-cell {
    font-size: 0.6rem;
  }

  /* Tablet scrollbar enhancements */
  .apply-enhanced-scroll::-webkit-scrollbar,
  .enhanced-scroll::-webkit-scrollbar,
  .family-type-info-modal::-webkit-scrollbar,
  .modal-content::-webkit-scrollbar,
  .table-responsive::-webkit-scrollbar,
  .destination-suggestions::-webkit-scrollbar,
  .card-preview-body::-webkit-scrollbar {
    width: 7px;
    height: 7px;
  }

  .apply-enhanced-scroll::-webkit-scrollbar-track,
  .enhanced-scroll::-webkit-scrollbar-track,
  .family-type-info-modal::-webkit-scrollbar-track,
  .modal-content::-webkit-scrollbar-track,
  .table-responsive::-webkit-scrollbar-track,
  .destination-suggestions::-webkit-scrollbar-track,
  .card-preview-body::-webkit-scrollbar-track {
    margin: 6px 0;
  }
}

@media (max-width: 480px) {
  .family-type-display {
    padding: 0.4rem;
    margin-top: 0.5rem;
  }

  .family-type-label {
    font-size: 0.8rem;
  }

  .family-type-name {
    font-size: 0.85rem;
  }

  .traveler-input-wrapper {
    gap: 0.25rem;
  }

  .family-type-info-btn {
    width: 32px;
    height: 32px;
  }

  .family-type-info-btn i {
    font-size: 0.8rem;
  }

  /* Ultra Compact Family Type Modal on Mobile */
  .family-type-info-modal {
    width: 98%;
    max-height: 80vh;
    margin: 0.125rem;
  }

  /* Mobile scroll indicator adjustments */
  .scroll-indicator {
    right: 6px;
    gap: 3px;
  }

  .scroll-dot {
    width: 2.5px;
    height: 2.5px;
  }

  /* Enhanced mobile scrollbar for all scrollable elements */
  .apply-enhanced-scroll::-webkit-scrollbar,
  .enhanced-scroll::-webkit-scrollbar,
  .family-type-info-modal::-webkit-scrollbar,
  .modal-content::-webkit-scrollbar,
  .table-responsive::-webkit-scrollbar,
  .destination-suggestions::-webkit-scrollbar,
  .card-preview-body::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .apply-enhanced-scroll::-webkit-scrollbar-thumb,
  .enhanced-scroll::-webkit-scrollbar-thumb,
  .family-type-info-modal::-webkit-scrollbar-thumb,
  .modal-content::-webkit-scrollbar-thumb,
  .table-responsive::-webkit-scrollbar-thumb,
  .destination-suggestions::-webkit-scrollbar-thumb,
  .card-preview-body::-webkit-scrollbar-thumb {
    border-radius: 3px;
  }

  .apply-enhanced-scroll::-webkit-scrollbar-track,
  .enhanced-scroll::-webkit-scrollbar-track,
  .family-type-info-modal::-webkit-scrollbar-track,
  .modal-content::-webkit-scrollbar-track,
  .table-responsive::-webkit-scrollbar-track,
  .destination-suggestions::-webkit-scrollbar-track,
  .card-preview-body::-webkit-scrollbar-track {
    margin: 4px 0;
  }

  .family-type-table {
    font-size: 0.6rem;
  }

  .family-type-table th,
  .family-type-table td {
    padding: 0.25rem 0.2rem;
  }

  .family-type-table th {
    font-size: 0.55rem;
  }

  .info-description {
    padding: 0.3rem;
    margin-bottom: 0.3rem;
  }

  .info-description p {
    font-size: 0.65rem;
  }

  .family-type-info-modal .family-type-name {
    font-size: 0.5rem;
  }

  .count-cell {
    font-size: 0.55rem;
  }

  .family-type-loading {
    padding: 0.75rem;
  }

  .loading-spinner {
    width: 25px;
    height: 25px;
  }

  .family-type-error {
    padding: 0.75rem;
  }

  .error-icon {
    font-size: 1.5rem;
  }

  .retry-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }

  .close-btn {
    padding: 0.4rem 1rem;
    font-size: 0.75rem;
  }
}

.destination-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: none;
}

.suggestion-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f3f4f6;
}

/* Optional Date Selector */
.optional-date-selector {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.optional-date-label {
  display: block;
  font-weight: 500;
  color: var(--text-clr);
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  opacity: 0.8;
}

.optional-date-label i {
  margin-right: 0.5rem;
  color: var(--primary);
  opacity: 0.7;
}

.date-dropdown {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  color: var(--text-clr);
  font-size: 0.9rem;
}

.date-dropdown:focus {
  background-color: white;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(21, 171, 139, 0.1);
}

.date-dropdown option {
  padding: 0.5rem;
}

/* Top Destinations Section */
.top-destinations-section {
  margin: 1rem 0;
  padding: 1rem 0;
}

.top-destinations-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.destinations-label {
  font-weight: 700;
  color: var(--text-heading-color);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.destinations-label i {
  color: var(--primary);
  font-size: 1.2rem;
}

.destinations-list {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  min-height: 60px;
}

.destination-chip {
  background: linear-gradient(135deg, #2491 0%, rgba(152, 251, 152, 0.5) 100%);
  color: var(--text-heading-color);
  border: 2px solid rgba(21, 171, 139, 0.2);
  padding: 0.75rem 1.25rem;
  border-radius: 25px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 0.6rem;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 4px 15px rgba(21, 171, 139, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  text-transform: capitalize;
  letter-spacing: 0.3px;
}

.destination-chip::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(21, 171, 139, 0.1), transparent);
  transition: left 0.6s ease;
}

.destination-chip:hover::before {
  left: 100%;
}

.destination-chip:hover {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: white;
  border-color: var(--primary);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 
    0 8px 25px rgba(21, 171, 139, 0.3),
    0 4px 15px rgba(21, 171, 139, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1);
}

.destination-chip:active {
  transform: translateY(-1px) scale(1.02);
  transition: all 0.1s ease;
}

.destination-chip i {
  font-size: 1rem;
  transition: all 0.3s ease;
}

.destination-chip:hover i {
  transform: scale(1.1);
}

/* Loading States */
.destinations-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  color: var(--text-clr);
  font-size: 0.9rem;
  padding: 1rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(21, 171, 139, 0.2);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Destination Icons Mapping */
.destination-chip[data-destination*="goa"] i::before { content: "\f5ca"; } /* beach */
.destination-chip[data-destination*="andaman"] i::before { content: "\f773"; } /* water */
.destination-chip[data-destination*="munnar"] i::before { content: "\f6fc"; } /* mountain */
.destination-chip[data-destination*="alleppey"] i::before { content: "\f1bb"; } /* tree */
.destination-chip[data-destination*="kerala"] i::before { content: "\f1bb"; } /* tree */
.destination-chip[data-destination*="kashmir"] i::before { content: "\f2dc"; } /* snowflake */
.destination-chip[data-destination*="manali"] i::before { content: "\f6fc"; } /* mountain */
.destination-chip[data-destination*="shimla"] i::before { content: "\f6fc"; } /* mountain */
.destination-chip[data-destination*="rajasthan"] i::before { content: "\f6be"; } /* fort */
.destination-chip[data-destination*="agra"] i::before { content: "\f6be"; } /* monument */

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .destinations-list {
    gap: 0.75rem;
    padding: 0 0.5rem;
  }

  .destination-chip {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
    gap: 0.5rem;
  }

  .destinations-label {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .destinations-list {
    gap: 0.5rem;
  }

  .destination-chip {
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
    border-radius: 20px;
  }
}

.search-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 1rem;
  width: 100%;
  box-shadow: 0 4px 15px rgba(21, 171, 139, 0.3);
}

.search-btn:hover {
  background: var(--secondary);
  box-shadow: 0 6px 20px rgba(21, 171, 139, 0.4);
  transform: translateY(-2px);
}

.family-type-display {
  margin-top: 1.5rem;
  text-align: center;
  padding: 1rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
}

.family-type-label {
  font-weight: 600;
  color: var(--text-clr);
  margin-right: 0.5rem;
}

.family-type-name {
  color: var(--primary);
  font-weight: 700;
  font-size: 1rem;
}

/* Hero Background */
.hero-background {
  display: none;
}

.hero-plane {
  position: absolute;
  width: 60px;
  height: auto;
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.hero-plane-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.hero-plane-2 {
  top: 60%;
  right: 15%;
  animation-delay: 3s;
}

.hero-decoration {
  position: absolute;
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  top: 10%;
  right: -200px;
  filter: blur(100px);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Results Section */
.results-section {
  padding: 4rem 2rem;
  background-color: rgba(21, 171, 139, 0.05);
  min-height: 50vh;
  display: none;
}

.results-section.show {
  display: block;
}

.results-container {
  max-width: 1200px;
  margin: 0 auto;
}

.results-header {
  text-align: center;
  margin-bottom: 3rem;
}

.results-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--black);
  line-height: 1.3;
}

.family-type-highlight {
  color: var(--x-2nd);
}

.destination-highlight {
  color: var(--green);
}

.date-highlight {
  color: var(--green);
}

/* Package Grid */
.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.package-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.package-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.package-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.package-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.package-card:hover .package-image img {
  transform: scale(1.05);
}

.duration-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.offer-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--primary);
  color: white;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.offer-badge.early-bird {
  background: var(--yellow);
  color: var(--black);
}

.offer-badge.best-value {
  background: var(--x-4);
}

.package-content {
  padding: 1.5rem;
}

.package-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: 1rem;
}

.package-inclusions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.inclusion-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-clr);
}

.inclusion-item i {
  color: var(--primary);
  font-size: 1.2rem;
}

.emi-highlight {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  text-align: center;
}

.emi-amount {
  font-size: 2rem;
  font-weight: 800;
  color: var(--x-2nd);
  margin-bottom: 0.5rem;
}

.emi-period {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-clr);
}

.emi-details {
  font-size: 1rem;
  color: var(--text-clr);
  margin-bottom: 0.5rem;
}

.total-amount {
  font-size: 0.9rem;
  color: var(--text-2);
}

.view-details-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: auto;
}

.view-details-btn:hover {
  background: var(--secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  scroll-behavior: smooth;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.5rem 0.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.modal-header h3 {
  font-size: 1rem;
  font-weight: 700;
  color: var(--black);
}

.modal-header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.print-btn {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  font-size: 0.9rem;
}

.print-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.print-btn:active {
  transform: translateY(0);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-clr);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f1f5f9;
  color: var(--black);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid #f1f5f9;
}

/* Traveler Modal */
.traveler-modal {
  max-width: 500px;
}

.traveler-counters {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.counter-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
}

.counter-info {
  display: flex;
  flex-direction: column;
}

.counter-label {
  font-weight: 600;
  color: var(--black);
  font-size: 1rem;
}

.counter-sublabel {
  font-size: 0.85rem;
  color: var(--text-clr);
}

.counter-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.counter-btn {
  width: 40px;
  height: 40px;
  border: 2px solid var(--x-2nd);
  background: white;
  color: var(--x-2nd);
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.counter-btn:hover {
  background: var(--x-2nd);
  color: white;
}

.counter-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--black);
  min-width: 30px;
  text-align: center;
}

.detected-family-type {
  background: rgba(139, 92, 246, 0.1);
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
}

.family-type-label {
  font-weight: 600;
  color: var(--text-clr);
  margin-right: 0.5rem;
}

.family-type-value {
  font-weight: 700;
  color: var(--x-2nd);
}

.apply-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, var(--x-2nd) 0%, var(--x-1st) 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

/* Package Modal */
.package-modal {
  max-width: 800px;
}

.image-gallery {
  margin-bottom: 2rem;
}

.main-image {
  width: 100%;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.package-tabs {
  display: flex;
  border-bottom: 2px solid #f1f5f9;
  margin-bottom: 2rem;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.package-tabs::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  background: transparent;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-clr);
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  margin-bottom: -1px;
  white-space: nowrap;
  flex-shrink: 0;
}

.tab-btn.active {
  color: var(--primary);
  border-bottom: 3px solid var(--primary);
}

.tab-btn:hover {
  color: var(--primary);
  background-color: rgba(21, 171, 139, 0.05);
}

.tab-content {
  min-height: 300px;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.package-overview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.overview-item {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.overview-item strong {
  color: var(--black);
  margin-right: 0.5rem;
}

.overview-item ul {
  margin-top: 0.5rem;
  margin-left: 1rem;
}

.overview-item li {
  margin-bottom: 0.25rem;
  color: var(--text-clr);
}

.itinerary-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.day-item {
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
}

.day-item h4 {
  color: var(--x-2nd);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.day-item p {
  color: var(--text-clr);
  line-height: 1.6;
}

.emi-plans {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.emi-plan {
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.emi-plan.best-value {
  border-color: var(--green);
  background: rgba(50, 214, 159, 0.05);
}

.emi-plan.selected-plan {
  border-color: var(--x-2nd);
  background: rgba(139, 92, 246, 0.1);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.emi-plan.selected-plan .select-plan-btn {
  background: var(--x-1st);
  color: white;
}

.emi-plan:hover {
  border-color: var(--x-2nd);
  transform: translateY(-4px);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.plan-header h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--black);
}

.plan-label {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
}

.plan-label.quick-pay {
  background: var(--yellow);
  color: var(--black);
}

.plan-label:not(.quick-pay):not(.low-monthly) {
  background: var(--green);
  color: white;
}

.plan-label.low-monthly {
  background: var(--x-4);
  color: white;
}

.plan-amount {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--x-2nd);
  margin-bottom: 1rem;
}

.plan-details {
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  color: var(--text-clr);
}

.plan-details div {
  margin-bottom: 0.25rem;
}

.prepaid-note {
  color: var(--primary) !important;
  font-weight: 600;
  font-size: 0.8rem;
}

/* EMI Loading State */
.emi-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  min-height: 200px;
}

.emi-loading-state .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(21, 171, 139, 0.2);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.emi-loading-state p {
  color: var(--gray-600);
  font-size: 0.9rem;
  margin: 0;
}

/* Package Loading State */
.package-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  min-height: 300px;
}

.package-loading-state .loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(21, 171, 139, 0.2);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

.package-loading-state p {
  color: var(--gray-600);
  font-size: 1rem;
  margin: 0;
  max-width: 300px;
}

/* Package Error State */
.package-error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  min-height: 300px;
}

.package-error-state .error-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.package-error-state .error-icon i {
  font-size: 24px;
  color: #ef4444;
}

.package-error-state h4 {
  color: var(--text-heading-color);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.package-error-state p {
  color: var(--gray-600);
  font-size: 1rem;
  margin: 0 0 2rem 0;
  max-width: 300px;
  line-height: 1.5;
}

.package-error-state .retry-btn {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.package-error-state .retry-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.select-plan-btn {
  width: 100%;
  padding: 0.75rem;
  background: var(--x-2nd);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-plan-btn:hover {
  background: var(--x-1st);
  transform: translateY(-2px);
}

.no-emi-plans {
  text-align: center;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #e5e7eb;
}

.no-emi-plans h3 {
  color: var(--text-clr);
  margin-bottom: 1rem;
}

.no-emi-plans p {
  color: var(--text-2);
  margin-bottom: 1.5rem;
}

/* Enhanced Package Overview Styles */
.package-summary-card {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(21, 171, 139, 0.2);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.summary-header h4 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.amenities-section .amenities-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  font-size: 1.4rem;
  font-weight: 600;
}

.package-badges {
  display: flex;
  gap: 8px;
}

.package-summary-card .offer-badge,
.package-summary-card .category-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-direction: row !important;
  align-items: center !important;
}

.detail-item i {
  width: 20px;
  opacity: 0.9;
}

.package-description {
  background: #f8f9ff;
  border-left: 4px solid #667eea;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
}

.package-description h5 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.package-description p {
  margin: 0;
  line-height: 1.6;
  color: #555;
}

/* Enhanced Inclusions and Exclusions Styles */
.package-inclusions, .package-exclusions {
  margin-bottom: 25px;
}

.package-inclusions h5, .package-exclusions h5 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.inclusions-grid, .exclusions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.package-inclusions .inclusion-item,
.package-exclusions .exclusion-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid transparent;
}

.package-inclusions .inclusion-item {
  border-left-color: #28a745;
}

.package-exclusions .exclusion-item {
  border-left-color: #dc3545;
}

.package-inclusions .inclusion-item i,
.package-exclusions .exclusion-item i {
  margin-top: 2px;
  font-size: 0.9rem;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-primary {
  color: #667eea !important;
}

/* Cost Breakdown Styles */
.cost-breakdown {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
}

.cost-breakdown h5 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
}

.breakdown-table {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.breakdown-row {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 15px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  align-items: center;
}

.breakdown-item {
  font-weight: 600;
  color: #333;
}

.breakdown-description {
  color: #666;
  font-size: 0.9rem;
}

.breakdown-cost {
  font-weight: 600;
  color: #667eea;
  text-align: right;
}

/* Additional Info Styles */
.additional-info {
  margin-bottom: 20px;
}

.info-grid {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.additional-info .info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background: #e8f2ff;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.package-validity {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  color: #856404;
  font-size: 0.9rem;
}

/* Enhanced Itinerary Styles */
.itinerary-header {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(21, 171, 139, 0.1) 0%, rgba(17, 138, 112, 0.1) 100%);
  border-radius: 12px;
  margin-bottom: 2rem;
}

.itinerary-header h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.itinerary-header p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.itinerary-timeline {
  position: relative;
  padding-left: 40px;
}

.itinerary-timeline::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #667eea, #764ba2);
}

.itinerary-timeline .day-item {
  position: relative;
  margin-bottom: 30px;
  padding: 0;
  border: none;
  background: none;
}

.day-marker {
  position: absolute;
  left: -30px;
  top: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.day-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(21, 171, 139, 0.3);
}

.day-line {
  width: 2px;
  height: 30px;
  background: #e9ecef;
  margin-top: 10px;
}

.day-item.last-day .day-line {
  display: none;
}

.day-content {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-left: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.day-content:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.day-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.day-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.day-badge.arrival {
  background-color: rgba(21, 171, 139, 0.1);
  color: var(--primary);
}

.day-badge.departure {
  background-color: rgba(239, 68, 68, 0.1);
  color: #EF4444;
}

.day-description {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.day-highlights {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9ff;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.day-highlights strong {
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.day-highlights ul {
  margin: 0;
  padding-left: 20px;
}

.day-highlights li {
  margin-bottom: 4px;
  color: #555;
}

.included-activities {
  margin-top: 30px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 12px;
  border: 1px solid #bae6fd;
}

.included-activities h5 {
  margin: 0 0 15px 0;
  color: #0369a1;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #333;
}

.activity-item i {
  color: #0369a1;
}

.itinerary-notes {
  margin-top: 30px;
  padding: 20px;
  background: #fffbeb;
  border: 1px solid #fbbf24;
  border-radius: 12px;
}

.itinerary-notes h6 {
  margin: 0 0 12px 0;
  color: #92400e;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.itinerary-notes ul {
  margin: 0;
  padding-left: 20px;
}

.itinerary-notes li {
  margin-bottom: 6px;
  color: #78350f;
  font-size: 0.9rem;
}

/* Destination Autocomplete Styles */
.destination-input-container {
  position: relative;
}

.destination-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
  scroll-behavior: smooth;
}

.destination-suggestions.show {
  display: block;
}

.suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
  background-color: #f8fafc;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-name {
  font-weight: 500;
  color: var(--text-clr);
}

.suggestion-category {
  font-size: 0.875rem;
  color: var(--text-2);
}

.suggestion-packages {
  font-size: 0.75rem;
  color: var(--x-1st);
  background: rgba(139, 92, 246, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Travel Card Styles (from arun.html) */
.travel-card {
  background: white;
  border-radius: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  width: 380px;
  position: relative;
  transition: all 0.3s ease;
  margin: 0 auto;
}

.travel-card:hover {
  transform: scale(1.05);
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
}

.travel-card .card-header {
  position: relative;
  height: 192px;
  background: linear-gradient(to bottom right, #34d399, #059669);
}

.travel-card .card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.travel-card .card-image[style*="opacity: 1"] {
  opacity: 1 !important;
}

.travel-card .image-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%);
  z-index: 1;
}

.travel-card .plan-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 0.15rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
  display: flex;
  align-items: center;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  z-index: 10;
}

.travel-card .plan-badge .crown-icon {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.travel-card .crown-icon {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}

.travel-card .duration-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 0.15rem 0.5rem;
  border-radius: 25px;
  font-size: 0.7rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
  z-index: 10;
  display: flex;
  align-items: center;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.travel-card .duration-badge .duration-icon {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.travel-card .location-overlay {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  z-index: 10;
}

.travel-card .destination-name-overlay {
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  margin-bottom: 0.25rem;
  line-height: 1.2;
  text-align: center;
  font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  letter-spacing: -0.025em;
}

.travel-card .location-overlay-text {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
  font-weight: 500;
}

.travel-card .tripxplo-logo {
  font-size: 1.25rem;
  font-weight: 800;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
  margin-bottom: 0.5rem;
  letter-spacing: 1px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.travel-card .card-content {
  padding: 1.5rem;
}

.travel-card .destination-info {
  display: none; /* Hidden since we're using overlay */
}

.travel-card .destination-name {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.travel-card .location {
  font-size: 0.875rem;
  color: #6b7280;
  font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.travel-card .family-type-main-section {
  margin-bottom: 1rem;
  margin-top: 0.3rem;
}

.travel-card .family-type-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #faf5ff;
  border-radius: 0.4rem;
  padding: 0.5rem;
}

.travel-card .family-type-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.travel-card .family-type-right {
  display: flex;
  align-items: center;
  margin-left: 0.5rem;
}

.travel-card .family-tripxplo-logo {
  height: 50px;
  width: auto;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.travel-card .family-tripxplo-logo:hover {
  opacity: 1;
}

/* Fallback logo styling for html2canvas compatibility */
.travel-card .family-tripxplo-logo-fallback {
  font-size: 16px;
  font-weight: 800;
  font-family: 'Inter';
  color: #4bc1a5;
  letter-spacing: 0.5px;
  display: inline-block;
  height: 24px;
  line-height: 24px;
  opacity: 0.9;
  position: relative;
  z-index: 10;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background: transparent;
  border: none;
  outline: none;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: visible;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: opacity 0.3s ease;
}

.travel-card .family-tripxplo-logo-fallback:hover {
  opacity: 1;
}

.travel-card .family-icon-wrapper-main {
  background: #e9d5ff;
  border-radius: 50%;
  padding: 0.3rem;
  margin-right: 0.5rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.travel-card .family-icon-main {
  color: #7c3aed;
  width: 16px;
  height: 16px;
}

.travel-card .family-type-text {
  color: #7c3aed;
  font-weight: 500;
  font-size: 0.8rem;
}

.travel-card .family-type-desc {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

.travel-card .amenities-section {
  margin-bottom: 1rem;
}

.travel-card .amenities-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.travel-card .amenities-list-vertical {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.travel-card .amenity-item-vertical {
  display: flex;
  align-items: center;
  background: #f9fafb;
  border-radius: 0.4rem;
  padding: 0.5rem;
  border-left: 2px solid #e5e7eb;
  transition: all 0.2s ease;
}

.travel-card .amenity-item-vertical:hover {
  border-left-color: #059669;
  background: #f0fdf4;
}

.travel-card .amenity-icon-wrapper-vertical {
  border-radius: 50%;
  padding: 0.3rem;
  margin-right: 0.5rem;
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.travel-card .amenity-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.travel-card .sightseeing-icon-wrapper {
  background: #dbeafe;
}

.travel-card .sightseeing-icon-wrapper .amenity-icon {
  color: #2563eb;
}

.travel-card .cab-icon-wrapper {
  background: #fef3c7;
}

.travel-card .cab-icon-wrapper .amenity-icon {
  color: #d97706;
}

.travel-card .meal-icon-wrapper {
  background: #fef2f2;
}

.travel-card .meal-icon-wrapper .amenity-icon {
  color: #dc2626;
}

.travel-card .hotel-icon-wrapper {
  background: #f3e8ff;
}

.travel-card .hotel-icon-wrapper .amenity-icon {
  color: #7c3aed;
}

.travel-card .flight-icon-wrapper {
  background: #ecfdf5;
}

.travel-card .flight-icon-wrapper .amenity-icon {
  color: #059669;
}

.travel-card .activity-icon-wrapper {
  background: #fff7ed;
}

.travel-card .activity-icon-wrapper .amenity-icon {
  color: #ea580c;
}

.travel-card .transport-icon-wrapper {
  background: #e0f2fe;
}

.travel-card .transport-icon-wrapper .amenity-icon {
  color: #0284c7;
}

.travel-card .accommodation-icon-wrapper {
  background: #f0fdf4;
}

.travel-card .accommodation-icon-wrapper .amenity-icon {
  color: #16a34a;
}

.travel-card .amenity-text-vertical {
  font-size: 0.75rem;
  flex: 1;
}

.travel-card .amenity-name-vertical {
  font-weight: 500;
  color: #1f2937;
  display: block;
  margin-bottom: 0.05rem;
}

.travel-card .amenity-desc-vertical {
  color: #6b7280;
  font-size: 0.7rem;
}

.travel-card .family-type-section {
  display: flex;
  align-items: center;
  background: #faf5ff;
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.travel-card .family-icon-wrapper {
  background: #e9d5ff;
  border-radius: 50%;
  padding: 0.5rem;
  margin-right: 0.75rem;
}

.travel-card .family-icon {
  color: #7c3aed;
  width: 16px;
  height: 16px;
}

.travel-card .family-text {
  font-size: 0.875rem;
}

.travel-card .family-label {
  font-weight: 500;
  color: #1f2937;
  display: block;
}

.travel-card .family-value {
  color: #6b7280;
}

.travel-card .price-section {
  padding: 0;
  text-align: center;
}

.travel-card .payment-plan {
  margin-bottom: 0.75rem;
}

.travel-card .payment-badge {
  font-size: 0.875rem;
  color: #059669;
  font-weight: 500;
  background: #ecfdf5;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
}

.travel-card .price-button {
  width: 100%;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 1rem 1.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.travel-card .price-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4);
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
}

.travel-card .price-main {
  font-size: 1.5rem;
  font-weight: bold;
}

.travel-card .price-desc {
  font-size: 0.875rem;
  opacity: 0.9;
}

.travel-card .price-info {
  margin-top: 0.5rem;
}

.travel-card .price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
}

/* Package Card Preview Modal - Google-like Design */
.card-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;
  padding: 1rem;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

.card-preview-content {
  background: white;
  border-radius: 24px;
  max-width: 600px;
  width: 100%;
  max-height: 95vh;
  overflow: hidden;
  position: relative;
  box-shadow: 
    0 24px 38px 3px rgba(0, 0, 0, 0.14),
    0 9px 46px 8px rgba(0, 0, 0, 0.12),
    0 11px 15px -7px rgba(0, 0, 0, 0.2);
  animation: modalSlideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(60px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(21, 171, 139, 0.1);
  background: linear-gradient(135deg, rgba(21, 171, 139, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.card-preview-header h3 {
  font-size: 1.375rem;
  font-weight: 500;
  color: var(--text-heading-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.card-preview-header h3::before {
  content: '🎫';
  font-size: 1.5rem;
}

.modal-close {
  background: rgba(21, 171, 139, 0.1);
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--primary);
  font-size: 1.25rem;
  font-weight: 400;
}

.modal-close:hover {
  background: var(--primary);
  color: white;
  transform: scale(1.1);
}

.card-preview-body {
  padding: 2rem;
  text-align: center;
  overflow-y: auto;
  max-height: calc(95vh - 140px);
  scroll-behavior: smooth;
}

.card-preview-image {
  margin-bottom: 2rem;
  position: relative;
}

.card-preview-image .travel-card {
  margin: 0 auto;
  box-shadow: 
    0 16px 32px rgba(21, 171, 139, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-preview-image .travel-card:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 20px 40px rgba(21, 171, 139, 0.2),
    0 12px 24px rgba(0, 0, 0, 0.15);
}

/* Enhanced Family Type Section with Theme Colors */
.travel-card .family-type-info {
  background: linear-gradient(135deg, rgba(21, 171, 139, 0.08) 0%, rgba(17, 138, 112, 0.05) 100%);
  border: 1px solid rgba(21, 171, 139, 0.15);
  border-radius: 12px;
  padding: 0.75rem;
}

.travel-card .family-icon-wrapper-main {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 50%;
  padding: 0.4rem;
  margin-right: 0.75rem;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(21, 171, 139, 0.3);
}

.travel-card .family-icon-main {
  color: white;
  width: 18px;
  height: 18px;
}

.travel-card .family-type-text {
  color: var(--primary);
  font-weight: 500;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.travel-card .family-type-desc {
  font-size: 0.8rem;
  color: var(--text-clr);
  margin: 0;
  font-weight: 400;
}

/* Enhanced What's Included Section */
.travel-card .amenities-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-heading-color);
  margin-bottom: 0.75rem;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.travel-card .amenities-title::before {
  content: '✨';
  font-size: 1.2rem;
}

.travel-card .amenity-name-vertical {
  font-weight: 500;
  color: var(--text-heading-color);
  display: block;
  margin-bottom: 0.1rem;
  font-size: 0.85rem;
  line-height: 1.3;
}

.travel-card .amenity-desc-vertical {
  color: var(--text-clr);
  font-size: 0.75rem;
  line-height: 1.2;
}

/* Enhanced Hotel Icon */
.travel-card .hotel-icon-wrapper {
  background: linear-gradient(135deg, rgba(21, 171, 139, 0.1) 0%, rgba(17, 138, 112, 0.05) 100%);
  border: 1px solid rgba(21, 171, 139, 0.2);
}

.travel-card .hotel-icon-wrapper .amenity-icon {
  color: var(--primary);
}

/* Package Name Display */
.travel-card .package-name-section {
  background: linear-gradient(135deg, rgba(21, 171, 139, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  border: 1px solid rgba(21, 171, 139, 0.1);
  border-radius: 12px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  text-align: center;
}

.travel-card .package-name-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary);
  margin: 0;
  letter-spacing: 0.3px;
}

.travel-card .package-name-subtitle {
  font-size: 0.8rem;
  color: var(--text-clr);
  margin: 0.25rem 0 0 0;
  font-weight: 400;
}

.card-preview-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(21, 171, 139, 0.1);
}

.download-btn, .download-card-btn, .preview-pdf-btn, .share-btn, .close-preview-btn, .view-emi-btn {
  padding: 0.875rem 1.75rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  justify-content: center;
}

.download-btn::before,
.download-card-btn::before,
.preview-pdf-btn::before,
.share-btn::before,
.close-preview-btn::before,
.view-emi-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.download-btn:hover::before,
.download-card-btn:hover::before,
.preview-pdf-btn:hover::before,
.share-btn:hover::before,
.close-preview-btn:hover::before,
.view-emi-btn:hover::before {
  left: 100%;
}

.download-btn {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(21, 171, 139, 0.3);
}

.download-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(21, 171, 139, 0.4);
}

.download-card-btn, .preview-pdf-btn, .view-emi-btn {
  background: var(--primary);
  color: white;
  box-shadow: 0 4px 15px #7fceb8;
}

.download-card-btn:hover, .preview-pdf-btn:hover, .view-emi-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px #77ccb4;
}

.share-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.share-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.close-preview-btn {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
}

.close-preview-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
}

.share-card-btn {
  transition: all 0.3s ease;
}

.share-card-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0.5rem 1rem;
    height: 60px; /* Slightly smaller on mobile */
  }

  .logo-image {
    height: 40px; /* Smaller logo on mobile */
    max-width: 120px;
  }

  .card-preview-modal {
    padding: 0.25rem;
  }

  .card-preview-content {
    border-radius: 16px;
    max-height: 98vh;
    max-width: 95vw;
    width: 95vw;
  }

  .card-preview-header {
    padding: 1rem 1.25rem;
  }

  .card-preview-header h3 {
    font-size: 1.1rem;
  }

  .card-preview-body {
    padding: 1.25rem;
    max-height: calc(98vh - 100px);
  }

  .card-preview-actions {
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
  }

  .download-btn, .download-card-btn, .preview-pdf-btn, .share-btn, .close-preview-btn, .view-emi-btn {
    width: 100%;
    justify-content: center;
    padding: 1rem 1.5rem;
    font-size: 0.95rem;
  }

  .travel-card .package-name-title {
    font-size: 1rem;
  }

  .travel-card .amenities-title {
    font-size: 1rem;
  }

  .travel-card {
    width: 340px;
  }

  .travel-card .card-content {
    padding: 1rem;
  }

  .travel-card .amenities-list-vertical {
    gap: 0.25rem;
  }

  .travel-card .amenity-item-vertical {
    padding: 0.4rem;
  }

  .travel-card .amenity-icon-wrapper-vertical {
    width: 28px;
    height: 28px;
    padding: 0.3rem;
  }

  .travel-card .amenity-icon {
    width: 12px;
    height: 12px;
  }

  .travel-card .family-icon-wrapper-main {
    width: 28px;
    height: 28px;
    padding: 0.3rem;
  }

  .travel-card .family-icon-main {
    width: 12px;
    height: 12px;
  }

  .travel-card .destination-name-overlay {
    font-size: 1.25rem;
    line-height: 1.3;
  }

  .travel-card .tripxplo-logo {
    font-size: 1rem;
  }

  .travel-card .amenity-text-vertical {
    font-size: 0.7rem;
  }

  .package-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    justify-content: center;
  }

  .tab-btn {
    flex: 1;
    min-width: calc(50% - 0.25rem);
    text-align: center;
    font-size: 0.85rem;
    padding: 0.75rem 0.5rem;
  }

  .tab-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    min-width: auto;
  }

  .share-card-btn {
    margin-left: 0;
    margin-top: 0.5rem;
    width: 100%;
  }

  .nav-links {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .nav-links a {
    display: none;
  }

  /* Show only auth button on mobile */
  .nav-links .auth-btn {
    display: flex;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    margin-left: 0;
  }

  /* Mobile auth modal adjustments */
  .auth-modal {
    max-width: 95%;
    margin: 1rem;
  }

  .auth-modal .modal-header h3 {
    font-size: 1.2rem;
  }

  .mobile-input-container {
    flex-direction: column;
  }

  .country-code {
    border-right: none;
    border-bottom: 1px solid var(--gray-300);
    text-align: center;
  }

  .pin-input {
    font-size: 1.2rem;
    letter-spacing: 0.3rem;
  }

  .user-menu-dropdown {
    right: 10px !important;
    min-width: 180px;
  }

  .user-menu-item {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
  }

  /* Mobile adjustments for profile and booking modals */
  .profile-modal,
  .booking-modal {
    max-width: 95%;
    margin: 1rem;
  }

  .profile-avatar i {
    font-size: 4rem;
  }

  .profile-stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-item {
    justify-content: center;
  }

  .booking-tabs {
    flex-direction: column;
  }

  .booking-tabs .tab-btn {
    padding: 0.75rem 1rem;
    text-align: left;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-state i {
    font-size: 3rem;
  }

  /* Mobile adjustments for booking cards */
  .booking-card {
    padding: 1rem;
  }

  .booking-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .booking-date {
    text-align: left;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    padding: 0.25rem 0;
  }

  .detail-value {
    text-align: left;
  }

  .booking-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-btn {
    padding: 0.875rem 1rem;
  }

  .hero-title {
    font-size: 2.8rem;
    letter-spacing: -0.01em;
  }

  .hero-title::after {
    width: 100px;
    height: 4px;
    bottom: -10px;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    max-width: 550px;
    gap: 0.8rem;
  }

  .hero-subtitle::before,
  .hero-subtitle::after {
    font-size: 1.1rem;
  }

  .search-form-container {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .destinations-list {
    gap: 0.5rem;
  }

  .destination-chip {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }

  .optional-date-selector {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
  }

  .optional-date-label {
    font-size: 0.8rem;
  }

  .package-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .results-title {
    font-size: 1.5rem;
  }

  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-content {
    max-height: 98vh;
    max-width: 95vw;
    width: 95vw;
  }

  /* Enhanced package modal mobile styles */
  .package-modal {
    max-width: 95vw;
    width: 95vw;
  }

  /* EMI plans mobile responsive improvements */
  .emi-plans {
    gap: 1rem;
  }

  .emi-plan {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .plan-amount {
    font-size: 1.25rem;
  }

  .plan-details {
    font-size: 0.85rem;
    margin-bottom: 1rem;
  }

  .select-plan-btn {
    padding: 0.875rem;
    font-size: 0.9rem;
  }

  /* EMI loading state mobile */
  .emi-loading-state {
    padding: 2rem 1rem;
    min-height: 150px;
  }

  .emi-loading-state .loading-spinner {
    width: 32px;
    height: 32px;
  }

  .emi-loading-state p {
    font-size: 0.85rem;
  }

  /* Package loading state mobile */
  .package-loading-state {
    padding: 2.5rem 1rem;
    min-height: 200px;
  }

  .package-loading-state .loading-spinner {
    width: 40px;
    height: 40px;
    border-width: 3px;
  }

  .package-loading-state p {
    font-size: 0.9rem;
  }

  /* Package error state mobile */
  .package-error-state {
    padding: 2.5rem 1rem;
    min-height: 200px;
  }

  .package-error-state .error-icon {
    width: 50px;
    height: 50px;
  }

  .package-error-state .error-icon i {
    font-size: 20px;
  }

  .package-error-state h4 {
    font-size: 1.1rem;
  }

  .package-error-state p {
    font-size: 0.9rem;
  }

  .package-error-state .retry-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1.5rem;
  }

  /* Enhanced Package Modal Responsive Styles */
  .detail-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .inclusions-grid, .exclusions-grid {
    grid-template-columns: 1fr;
  }

  .breakdown-row {
    grid-template-columns: 1fr;
    gap: 8px;
    text-align: left;
  }

  .breakdown-cost {
    text-align: left;
  }

  .info-grid {
    flex-direction: column;
    gap: 10px;
  }

  .activities-grid {
    grid-template-columns: 1fr;
  }

  .itinerary-timeline {
    padding-left: 30px;
  }

  .day-marker {
    left: -25px;
  }

  .day-number {
    width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }

  .day-content {
    margin-left: 15px;
    padding: 15px;
  }

  .day-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .package-summary-card {
    padding: 1.5rem;
    margin: 0 0.5rem;
    border-radius: 12px;
  }

  .summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 1rem;
  }

  .summary-header h4 {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
  }

  .amenities-section .amenities-grid {
    font-size: 1.2rem;
    gap: 0.5rem;
  }

  .package-badges {
    flex-wrap: wrap;
    gap: 6px;
  }

  .package-summary-card .offer-badge,
  .package-summary-card .category-badge {
    padding: 3px 8px;
    font-size: 0.75rem;
  }

  .summary-details {
    gap: 12px;
  }

  .detail-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .detail-item {
    gap: 8px;
  }

  .detail-item i {
    width: 16px;
    font-size: 0.9rem;
  }

  .package-description {
    padding: 15px;
    margin-bottom: 20px;
  }

  .package-description h5 {
    font-size: 1rem;
    margin-bottom: 10px;
  }

  .package-description p {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .emi-plans {
    grid-template-columns: 1fr;
  }

  .package-inclusions, .package-exclusions {
    margin-bottom: 20px;
  }

  .package-inclusions h5, .package-exclusions h5 {
    font-size: 1rem;
    margin-bottom: 12px;
  }

  .inclusions-grid, .exclusions-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .package-inclusions .inclusion-item,
  .package-exclusions .exclusion-item {
    padding: 10px;
    gap: 8px;
  }

  .package-inclusions .inclusion-item i,
  .package-exclusions .exclusion-item i {
    font-size: 0.85rem;
  }

  .cost-breakdown {
    padding: 15px;
    margin-bottom: 20px;
  }

  .cost-breakdown h5 {
    font-size: 1rem;
    margin-bottom: 12px;
  }

  .breakdown-row {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
  }

  .breakdown-item {
    font-size: 0.9rem;
  }

  .breakdown-description {
    font-size: 0.85rem;
  }

  .breakdown-cost {
    font-size: 0.9rem;
    text-align: left;
  }

  .additional-info {
    margin-bottom: 15px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .additional-info .info-item {
    padding: 10px;
    gap: 8px;
  }

  .counter-group {
    padding: 0.75rem;
  }

  .hero-plane {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 6rem 1rem 2rem;
  }

  .hero-title {
    font-size: 2.2rem;
    letter-spacing: 0;
  }

  .hero-title::after {
    width: 80px;
    height: 3px;
    bottom: -8px;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    max-width: 500px;
    gap: 0.6rem;
  }

  .hero-subtitle::before,
  .hero-subtitle::after {
    font-size: 1rem;
  }

  .search-form-container {
    padding: 1rem;
  }

  .destination-chip {
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
  }

  .destinations-list {
    gap: 0.4rem;
  }

  .package-card {
    margin: 0 0.5rem;
  }

  .package-tabs {
    margin-bottom: 1.5rem;
  }

  .tab-btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  /* Ultra mobile responsive for card preview */
  .card-preview-modal {
    padding: 0.125rem;
  }

  .card-preview-content {
    max-width: 98vw;
    width: 98vw;
    border-radius: 12px;
  }

  .card-preview-header {
    padding: 0.875rem 1rem;
  }

  .card-preview-header h3 {
    font-size: 1rem;
  }

  .card-preview-body {
    padding: 1rem;
    max-height: calc(98vh - 90px);
  }

  /* Package modal ultra mobile */
  .package-modal {
    max-width: 98vw;
    width: 98vw;
  }

  .modal-content {
    max-width: 98vw;
    width: 98vw;
  }

  /* EMI plans ultra mobile */
  .emi-plan {
    padding: 0.875rem;
  }

  .plan-amount {
    font-size: 1.1rem;
  }

  .plan-details {
    font-size: 0.8rem;
  }

  .select-plan-btn {
    padding: 0.75rem;
    font-size: 0.85rem;
  }

  /* EMI loading state ultra mobile */
  .emi-loading-state {
    padding: 1.5rem 0.75rem;
    min-height: 120px;
  }

  .emi-loading-state .loading-spinner {
    width: 28px;
    height: 28px;
    border-width: 2px;
  }

  .emi-loading-state p {
    font-size: 0.8rem;
  }

  /* Package loading state ultra mobile */
  .package-loading-state {
    padding: 2rem 0.75rem;
    min-height: 150px;
  }

  .package-loading-state .loading-spinner {
    width: 35px;
    height: 35px;
    border-width: 2px;
  }

  .package-loading-state p {
    font-size: 0.85rem;
  }

  /* Package error state ultra mobile */
  .package-error-state {
    padding: 2rem 0.75rem;
    min-height: 150px;
  }

  .package-error-state .error-icon {
    width: 45px;
    height: 45px;
  }

  .package-error-state .error-icon i {
    font-size: 18px;
  }

  .package-error-state h4 {
    font-size: 1rem;
  }

  .package-error-state p {
    font-size: 0.85rem;
  }

  .package-error-state .retry-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .package-summary-card {
    padding: 1rem;
    margin: 0 0.25rem;
    border-radius: 10px;
  }

  .summary-header {
    gap: 8px;
    margin-bottom: 0.75rem;
  }

  .summary-header h4 {
    font-size: 0.8rem;
  }

  .amenities-section .amenities-grid {
    font-size: 1.1rem;
    gap: 0.4rem;
  }

  .package-summary-card .offer-badge,
  .package-summary-card .category-badge {
    padding: 2px 6px;
    font-size: 0.7rem;
  }

  .detail-item {
    gap: 6px;
  }

  .detail-item i {
    width: 14px;
    font-size: 0.8rem;
  }

  .package-description {
    padding: 12px;
    margin-bottom: 15px;
  }

  .package-description h5 {
    font-size: 0.95rem;
    margin-bottom: 8px;
  }

  .package-description p {
    font-size: 0.85rem;
  }

  .package-inclusions h5, .package-exclusions h5 {
    font-size: 0.95rem;
    margin-bottom: 10px;
  }

  .package-inclusions .inclusion-item,
  .package-exclusions .exclusion-item {
    padding: 8px;
    gap: 6px;
  }

  .package-inclusions .inclusion-item i,
  .package-exclusions .exclusion-item i {
    font-size: 0.8rem;
  }

  .cost-breakdown {
    padding: 12px;
    margin-bottom: 15px;
  }

  .cost-breakdown h5 {
    font-size: 0.95rem;
    margin-bottom: 10px;
  }

  .breakdown-row {
    padding: 8px;
    gap: 8px;
  }

  .breakdown-item {
    font-size: 0.85rem;
  }

  .breakdown-description {
    font-size: 0.8rem;
  }

  .breakdown-cost {
    font-size: 0.85rem;
  }

  .additional-info .info-item {
    padding: 8px;
    gap: 6px;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus States for Accessibility */
.form-input:focus,
.search-btn:focus,
.view-details-btn:focus,
.apply-btn:focus,
.select-plan-btn:focus,
.counter-btn:focus,
.tab-btn:focus {
  outline: 2px solid var(--x-2nd);
  outline-offset: 2px;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--x-2nd);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Features Section */
.features-section {
  background: #f8fafc;
  padding: 5rem 2rem;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features-header {
  text-align: center;
  margin-bottom: 4rem;
}

.features-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: 1rem;
}

.features-subtitle {
  font-size: 1.2rem;
  color: var(--text-clr);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 8px 25px rgba(21, 171, 139, 0.3);
  transition: all 0.3s ease;
}

.feature-icon i {
  font-size: 2rem;
  color: white;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--text-clr);
  line-height: 1.6;
}

.feature-card:hover .feature-icon {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(21, 171, 139, 0.4);
}

/* Footer Section */
.footer-section {
  background: linear-gradient(135deg, var(--text-heading-color) 0%, var(--x-1st) 100%);
  color: white;
  padding: 4rem 0 2rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  margin-bottom: 3rem;
}

.footer-brand {
  max-width: 400px;
}

.footer-logo {
  font-size: 2rem;
  font-weight: 800;
  letter-spacing: 1px;
  margin-bottom: 1.5rem;
}

.footer-logo .logo-trip {
  color: var(--primary);
}

.footer-logo .logo-xplo {
  color: white;
}

.footer-tagline {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-size: 1rem;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
}

.footer-column h4 {
  color: var(--primary);
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.footer-column a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  display: block;
  padding: 0.5rem 0;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  padding-left: 0.75rem;
  font-size: 0.95rem;
}

.footer-column a:hover {
  color: var(--primary);
  border-left-color: var(--primary);
  padding-left: 1rem;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 2rem;
}

.footer-address {
  flex: 1;
  max-width: 400px;
}

.footer-address h5 {
  color: var(--primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.footer-address p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-size: 0.9rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(21, 171, 139, 0.4);
  border-color: var(--primary);
}

.footer-copyright {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-copyright p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .footer-address {
    max-width: none;
  }

  .footer-social {
    justify-content: center;
  }

  .footer-column a {
    border-left: none;
    padding-left: 0;
  }

  .footer-column a:hover {
    padding-left: 0;
    border-left: none;
  }
}

.travel-agency .footer {
  position: absolute;
  width: 1628px;
  height: 634px;
  top: 5077px;
  left: 184px;
  background-color: transparent;
}

.travel-agency .overlap-group {
  position: absolute;
  width: 750px;
  height: 514px;
  top: 120px;
  left: 870px;
}

.travel-agency .decore {
  position: absolute;
  width: 479px;
  height: 497px;
  top: 17px;
  left: 271px;
  background-color: var(--d-2);
  border-radius: 239.47px/248.43px;
  transform: rotate(180deg);
  filter: blur(150px);
  opacity: 0.4;
}

.travel-agency .najam-centre {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #5e6282;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
}

.travel-agency .outbound {
  position: absolute;
  width: 111px;
  height: 71px;
  top: 21px;
  left: 850px;
}

.travel-agency .social {
  position: absolute;
  width: 131px;
  height: 65px;
  top: -8px;
  left: -10px;
}

.travel-agency .overlap {
  position: absolute;
  width: 257px;
  height: 117px;
  top: 0;
  left: 0;
}

.travel-agency .company-desc {
  position: absolute;
  width: 255px;
  height: 117px;
  top: 0;
  left: 2px;
}

.travel-agency .group {
  position: absolute;
  width: 183px;
  height: 53px;
  top: 0;
  left: 0;
}

.travel-agency .rectangle {
  position: absolute;
  width: 84px;
  height: 12px;
  top: 41px;
  left: 89px;
  background-color: #fff1da;
  border-radius: 7px;
}

.travel-agency .div {
  position: absolute;
  width: 20px;
  height: 12px;
  top: 0;
  left: 163px;
  background-color: #fff1da;
  border-radius: 7px;
}

.travel-agency .rectangle-2 {
  position: absolute;
  width: 20px;
  height: 12px;
  top: 41px;
  left: 0;
  background-color: #fff1da;
  border-radius: 7px;
}

.travel-agency .make-your-dream {
  position: absolute;
  top: 85px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 13px;
  letter-spacing: 0;
  line-height: 16.2px;
}

.travel-agency .TRIPMILESTONE {
  position: absolute;
  top: 25px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 800;
  color: transparent;
  font-size: 26px;
  letter-spacing: 1.56px;
  line-height: normal;
}

.travel-agency .text-wrapper {
  color: #3dbb74;
}

.travel-agency .span {
  color: #181e4b;
}

.travel-agency .nav-columns {
  width: 534px;
  top: 21px;
  left: 292px;
  position: absolute;
  height: 82px;
}

.travel-agency .nav-row {
  width: 112px;
  top: 0;
  left: 0;
  position: absolute;
  height: 82px;
}

.travel-agency .text-wrapper-2 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: var(--black);
  font-size: 21px;
  letter-spacing: 0;
  line-height: 26.1px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-3 {
  position: absolute;
  top: 60px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .nav-row-2 {
  width: 93px;
  height: 80px;
  left: 180px;
  position: absolute;
  top: 0;
}

.travel-agency .text-wrapper-4 {
  position: absolute;
  top: 0;
  left: 1px;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: var(--black);
  font-size: 21px;
  letter-spacing: 0;
  line-height: 26.1px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-5 {
  position: absolute;
  top: 58px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .nav-row-3 {
  width: 178px;
  height: 82px;
  left: 360px;
  position: absolute;
  top: 0;
}

.travel-agency .copyrights {
  position: absolute;
  top: 258px;
  left: 432px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-6 {
  position: absolute;
  top: 95px;
  left: 870px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #5e6282;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  white-space: nowrap;
}

.travel-agency .overlap-2 {
  position: absolute;
  width: 1280px;
  height: 4940px;
  top: 47px;
  left: 131px;
}

.travel-agency .book-a-trip {
  position: absolute;
  width: 1047px;
  height: 492px;
  top: 2370px;
  left: 55px;
}

.travel-agency .heading {
  position: absolute;
  top: 42px;
  left: 0;
  font-family: "Volkhov", Helvetica;
  font-weight: 700;
  color: #14183e;
  font-size: 50px;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .image {
  position: absolute;
  width: 414px;
  height: 459px;
  top: 19px;
  left: 629px;
}

.travel-agency .overlap-3 {
  position: relative;
  height: 459px;
}

.travel-agency .group-2 {
  position: absolute;
  width: 354px;
  height: 367px;
  top: 0;
  left: 60px;
  background-color: #59b1e6cc;
  border-radius: 177px/183.5px;
  filter: blur(150px);
  opacity: 0.4;
}

.travel-agency .overlap-wrapper {
  position: absolute;
  width: 370px;
  height: 400px;
  top: 59px;
  left: 0;
}

.travel-agency .overlap-4 {
  position: relative;
  width: 378px;
  height: 400px;
}

.travel-agency .overlap-group-wrapper {
  position: absolute;
  width: 378px;
  height: 400px;
  top: 0;
  left: 0;
}

.travel-agency .overlap-group-2 {
  position: relative;
  width: 370px;
  height: 400px;
  background-color: #ffffff;
  border-radius: 26px;
  box-shadow: 0px 1.85px 3.15px #00000001, 0px 8.15px 6.52px #00000002, 0px 20px 13px #00000003,
    0px 38.52px 25.48px #00000003, 0px 64.81px 46.85px #00000004, 0px 100px 80px #00000005;
}

.travel-agency .OPTIONS {
  position: absolute;
  width: 139px;
  height: 36px;
  top: 283px;
  left: 28px;
}

.travel-agency .LEAF {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 0;
  background-color: var(--circle);
  border-radius: 18px;
}

.travel-agency .img {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 12px;
  left: 11px;
}

.travel-agency .send {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 103px;
  background-color: var(--circle);
  border-radius: 18px;
}

.travel-agency .map-icon {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 54px;
  background-color: var(--circle);
  border-radius: 18px;
}

.travel-agency .map {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 11px;
  left: 10px;
}

.travel-agency .building {
  position: absolute;
  width: 16px;
  height: 16px;
  top: 348px;
  left: 25px;
}

.travel-agency .text-wrapper-7 {
  position: absolute;
  top: 349px;
  left: 56px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-2);
  font-size: 16px;
  letter-spacing: -0.32px;
  line-height: 19.9px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-8 {
  position: absolute;
  top: 243px;
  left: 25px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .element-adults-children {
  top: 242px;
  left: 124px;
  color: var(--text-2);
  font-size: 16px;
  letter-spacing: -0.08px;
  line-height: 19.9px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .rectangle-3 {
  position: absolute;
  width: 321px;
  height: 161px;
  top: 20px;
  left: 25px;
}

.travel-agency .text-wrapper-9 {
  position: absolute;
  top: 207px;
  left: 25px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--black);
  font-size: 18px;
  letter-spacing: 0.27px;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .line {
  position: absolute;
  width: 1px;
  height: 16px;
  top: 243px;
  left: 116px;
  object-fit: cover;
}

.travel-agency .heart {
  position: absolute;
  width: 1px;
  height: 1px;
  top: 350px;
  left: 325px;
}

.travel-agency .subheading {
  position: absolute;
  top: 0;
  left: 3px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .values {
  position: absolute;
  width: 589px;
  height: 289px;
  top: 203px;
  left: 0;
}

.travel-agency .value {
  position: absolute;
  width: 593px;
  height: 84px;
  top: 0;
  left: 0;
}

.travel-agency .selection-wrapper {
  position: absolute;
  width: 47px;
  height: 48px;
  top: 4px;
  left: 0;
  background-color: var(--x-3);
  border-radius: 13px;
}

.travel-agency .selection {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 13px;
  left: 13px;
}

.travel-agency .text-wrapper-10 {
  position: absolute;
  top: 0;
  left: 68px;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: var(--text-clr);
  font-size: 16px;
  letter-spacing: 0;
  line-height: 19.9px;
  white-space: nowrap;
}

.travel-agency .p {
  position: absolute;
  top: 24px;
  left: 68px;
  font-family: "Poppins", Helvetica;
  font-weight: 400;
  color: var(--text-clr);
  font-size: 16px;
  letter-spacing: 0;
  line-height: 19.9px;
}

.travel-agency .value-2 {
  width: 507px;
  height: 65px;
  top: 224px;
  position: absolute;
  left: 0;
}

.travel-agency .taxi-wrapper {
  position: absolute;
  width: 47px;
  height: 48px;
  top: 4px;
  left: 0;
  background-color: var(--x-5);
  border-radius: 13px;
}

.travel-agency .taxi {
  position: absolute;
  width: 22px;
  height: 19px;
  top: 16px;
  left: 13px;
}

.travel-agency .pay-the-first {
  position: absolute;
  top: 25px;
  left: 68px;
  font-family: "Poppins", Helvetica;
  font-weight: 400;
  color: var(--text-clr);
  font-size: 16px;
  letter-spacing: 0;
  line-height: 19.9px;
}

.travel-agency .value-3 {
  width: 561px;
  height: 84px;
  top: 112px;
  position: absolute;
  left: 0;
}

.travel-agency .water-sport-wrapper {
  position: absolute;
  width: 47px;
  height: 48px;
  top: 4px;
  left: 0;
  background-color: var(--x-4);
  border-radius: 13px;
}

.travel-agency .water-sport {
  position: absolute;
  width: 22px;
  height: 18px;
  top: 14px;
  left: 13px;
}

.travel-agency .destinations {
  position: absolute;
  width: 1052px;
  height: 3235px;
  top: 1567px;
  left: 42px;
}

.travel-agency .destination {
  position: absolute;
  width: 321px;
  height: 457px;
  top: 160px;
  left: 725px;
}

.travel-agency .overlap-5 {
  position: relative;
  width: 474px;
  height: 638px;
  top: -1px;
  left: -79px;
  background-image: url(./img/rectangle-14-4.png);
  background-size: cover;
  background-position: 50% 50%;
}

.travel-agency .card {
  position: absolute;
  width: 319px;
  height: 130px;
  top: 328px;
  left: 79px;
}

.travel-agency .overlap-group-3 {
  position: relative;
  width: 315px;
  height: 130px;
  background-color: #ffffff;
  border-radius: 0px 0px 24px 24px;
}

.travel-agency .text-wrapper-11 {
  position: absolute;
  top: 18px;
  left: 20px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-12 {
  position: absolute;
  top: 18px;
  left: 240px;
  font-family: "Open Sans", Helvetica;
  font-weight: 700;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-13 {
  position: absolute;
  top: 407px;
  left: 319px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-14 {
  position: absolute;
  top: 390px;
  left: 99px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-15 {
  top: 409px;
  left: 99px;
  color: #c4c4c4;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .destination-2 {
  width: 315px;
  left: 0;
  position: absolute;
  height: 457px;
  top: 160px;
}

.travel-agency .card-wrapper {
  position: relative;
  width: 474px;
  height: 638px;
  top: -1px;
  left: -79px;
  background-image: url(./img/rectangle-14-2.png);
  background-size: cover;
  background-position: 50% 50%;
}

.travel-agency .div-wrapper {
  position: relative;
  width: 325px;
  height: 130px;
  top: 328px;
  left: 79px;
}

.travel-agency .text-wrapper-16 {
  position: absolute;
  top: 17px;
  left: 20px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-17 {
  top: 79px;
  left: 234px;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .text-wrapper-18 {
  top: 62px;
  left: 20px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  line-height: 17.4px;
  white-space: nowrap;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .element-adults-child {
  top: 81px;
  left: 20px;
  color: #c4c4c4;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .text-wrapper-19 {
  position: absolute;
  top: 18px;
  left: 230px;
  font-family: "Open Sans", Helvetica;
  font-weight: 700;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .destination-3 {
  width: 321px;
  left: 350px;
  position: absolute;
  height: 457px;
  top: 160px;
}

.travel-agency .overlap-6 {
  position: relative;
  width: 474px;
  height: 638px;
  top: -1px;
  left: -79px;
  background-image: url(./img/rectangle-14.png);
  background-size: cover;
  background-position: 50% 50%;
}

.travel-agency .subheading-2 {
  position: absolute;
  top: 0;
  left: 459px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .heading-2 {
  top: 35px;
  left: 317px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-3 {
  top: 1362px;
  left: 287px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-4 {
  top: 2308px;
  left: 381px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-5 {
  top: 3170px;
  left: 375px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-6 {
  top: 3033px;
  left: 493px;
  font-family: "Inter", Helvetica;
  color: #3dbb74;
  font-size: 22px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .services {
  position: absolute;
  width: 1237px;
  height: 525px;
  top: 919px;
  left: 18px;
}

.travel-agency .group-3 {
  position: absolute;
  width: 197px;
  height: 166px;
  top: 0;
  left: 1080px;
}

.travel-agency .text-wrapper-20 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--yellow);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-21 {
  position: absolute;
  top: 35px;
  left: 0;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-22 {
  position: absolute;
  top: 70px;
  left: 0;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-23 {
  position: absolute;
  top: 0;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-24 {
  position: absolute;
  top: 35px;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-25 {
  position: absolute;
  top: 70px;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-26 {
  position: absolute;
  top: 105px;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-27 {
  position: absolute;
  top: 0;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-28 {
  position: absolute;
  top: 35px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-29 {
  position: absolute;
  top: 70px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-2nd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-30 {
  position: absolute;
  top: 105px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-31 {
  position: absolute;
  top: 140px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-32 {
  position: absolute;
  top: 0;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-33 {
  position: absolute;
  top: 35px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-34 {
  position: absolute;
  top: 70px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-35 {
  position: absolute;
  top: 105px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-36 {
  position: absolute;
  top: 140px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-37 {
  position: absolute;
  top: 0;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-38 {
  position: absolute;
  top: 35px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-39 {
  position: absolute;
  top: 70px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-40 {
  position: absolute;
  top: 105px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-41 {
  position: absolute;
  top: 140px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-42 {
  position: absolute;
  top: 8px;
  left: 503px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .overlap-7 {
  position: absolute;
  width: 878px;
  height: 480px;
  top: 45px;
  left: 0;
}

.travel-agency .overlap-8 {
  position: absolute;
  width: 878px;
  height: 480px;
  top: 0;
  left: 0;
}

.travel-agency .why-family-EMI {
  position: absolute;
  top: 0;
  left: 226px;
  font-family: "Volkhov", Helvetica;
  font-weight: 700;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .category-active {
  position: absolute;
  width: 306px;
  height: 347px;
  top: 133px;
  left: 233px;
}

.travel-agency .overlap-9 {
  position: relative;
  width: 302px;
  height: 347px;
}

.travel-agency .rectangle-4 {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 247px;
  left: 0;
  background-color: #df6951;
  border-radius: 30px 0px 10px 0px;
}

.travel-agency .rectangle-5 {
  position: absolute;
  width: 267px;
  height: 314px;
  top: 0;
  left: 35px;
  background-color: var(--white);
  border-radius: 36px;
  box-shadow: 0px 1.85px 3.15px #00000001, 0px 8.15px 6.52px #00000002, 0px 20px 13px #00000003,
    0px 38.52px 25.48px #00000003, 0px 64.81px 46.85px #00000004, 0px 100px 80px #00000005;
}

.travel-agency .swap-plans-days {
  position: absolute;
  width: 181px;
  top: 191px;
  left: 79px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-43 {
  position: absolute;
  top: 149px;
  left: 109px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .group-4 {
  position: absolute;
  width: 86px;
  height: 66px;
  top: 40px;
  left: 132px;
}

.travel-agency .overlap-group-4 {
  position: relative;
  height: 66px;
}

.travel-agency .rectangle-6 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 0;
  left: 0;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
}

.travel-agency .vector {
  position: absolute;
  width: 60px;
  height: 60px;
  top: 6px;
  left: 25px;
}

.travel-agency .category {
  position: absolute;
  width: 405px;
  height: 378px;
  top: 47px;
  left: 0;
}

.travel-agency .overlap-10 {
  position: relative;
  width: 401px;
  height: 378px;
}

.travel-agency .noun-time {
  position: absolute;
  width: 302px;
  height: 378px;
  top: 0;
  left: 99px;
}

.travel-agency .group-5 {
  position: absolute;
  width: 79px;
  height: 74px;
  top: 132px;
  left: 71px;
}

.travel-agency .overlap-group-5 {
  position: relative;
  height: 74px;
}

.travel-agency .rectangle-7 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 25px;
  left: 29px;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
  transform: rotate(-180deg);
}

.travel-agency .vector-2 {
  position: absolute;
  width: 58px;
  height: 58px;
  top: 0;
  left: 0;
}

.travel-agency .text-wrapper-44 {
  position: absolute;
  width: 181px;
  top: 277px;
  left: 10px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-45 {
  position: absolute;
  top: 235px;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .category-2 {
  position: absolute;
  width: 231px;
  height: 229px;
  top: 173px;
  left: 590px;
}

.travel-agency .group-6 {
  position: absolute;
  width: 73px;
  height: 67px;
  top: 0;
  left: 107px;
}

.travel-agency .overlap-group-6 {
  position: relative;
  height: 67px;
}

.travel-agency .rectangle-8 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 0;
  left: 0;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
  transform: rotate(180deg);
}

.travel-agency .vector-3 {
  position: absolute;
  width: 48px;
  height: 61px;
  top: 6px;
  left: 25px;
}

.travel-agency .text-wrapper-46 {
  position: absolute;
  width: 181px;
  top: 151px;
  left: 24px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-47 {
  position: absolute;
  top: 109px;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .category-3 {
  position: absolute;
  width: 201px;
  height: 223px;
  top: 224px;
  left: 905px;
}

.travel-agency .group-7 {
  position: absolute;
  width: 77px;
  height: 73px;
  top: 0;
  left: 98px;
}

.travel-agency .overlap-group-7 {
  position: relative;
  height: 73px;
}

.travel-agency .rectangle-9 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 24px;
  left: 0;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
}

.travel-agency .vector-4 {
  position: absolute;
  width: 55px;
  height: 55px;
  top: 0;
  left: 22px;
}

.travel-agency .text-wrapper-48 {
  position: absolute;
  width: 181px;
  top: 145px;
  left: 8px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-49 {
  position: absolute;
  top: 103px;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .hero {
  position: absolute;
  width: 1270px;
  height: 4940px;
  top: 0;
  left: 10px;
}

.travel-agency .overlap-11 {
  position: relative;
  height: 4940px;
}

.travel-agency .top-nav {
  position: absolute;
  width: 1270px;
  height: 55px;
  top: 0;
  left: 0;
}

.travel-agency .hero-content {
  position: absolute;
  width: 1201px;
  height: 4924px;
  top: 16px;
  left: 7px;
}

.travel-agency .overlap-12 {
  position: relative;
  height: 4924px;
}

.travel-agency .desc {
  position: absolute;
  width: 696px;
  height: 4770px;
  top: 154px;
  left: 0;
}

.travel-agency .CTA {
  position: absolute;
  width: 649px;
  height: 4295px;
  top: 475px;
  left: 0;
}

.travel-agency .CTA-2 {
  top: -15px;
  left: -35px;
  position: absolute;
  width: 240px;
  height: 130px;
}

.travel-agency .CTA-3 {
  top: 4220px;
  left: 444px;
  position: absolute;
  width: 240px;
  height: 130px;
}

.travel-agency .tagline {
  position: absolute;
  top: 0;
  left: 2px;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: #df6951;
  font-size: 20px;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .desc-2 {
  position: absolute;
  width: 477px;
  top: 351px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #5e6282;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 30px;
}

.travel-agency .overlap-group-8 {
  position: absolute;
  width: 688px;
  height: 178px;
  top: 54px;
  left: 2px;
}

.travel-agency .decore-2 {
  position: absolute;
  width: 385px;
  height: 12px;
  top: 69px;
  left: 303px;
}

.travel-agency .heading-7 {
  top: 0;
  left: 0;
  font-family: "Volkhov", Helvetica;
  color: var(--x-1st);
  font-size: 84px;
  letter-spacing: -3.36px;
  line-height: 89px;
  position: absolute;
  font-weight: 700;
}

.travel-agency .image-2 {
  position: absolute;
  width: 692px;
  height: 785px;
  top: 0;
  left: 509px;
}

.travel-agency .overlap-13 {
  position: relative;
  height: 785px;
}

.travel-agency .plane {
  position: absolute;
  width: 167px;
  height: 153px;
  top: 145px;
  left: 525px;
}

.travel-agency .family-image {
  position: absolute;
  width: 595px;
  height: 785px;
  top: 0;
  left: 0;
  background-image: url(./img/family-holiday-1-1.png);
  background-size: 100% 100%;
}

.travel-agency .plane-2 {
  position: absolute;
  width: 167px;
  height: 153px;
  top: 98px;
  left: 41px;
}

.travel-agency .GOLD-CARD {
  position: absolute;
  width: 351px;
  height: 743px;
  top: 3040px;
  left: 402px;
}

.travel-agency .SILVER-CARD {
  position: absolute;
  width: 351px;
  height: 743px;
  top: 3033px;
  left: 22px;
}

.travel-agency .PLATINUM {
  position: absolute;
  width: 351px;
  height: 743px;
  top: 3046px;
  left: 782px;
}

.travel-agency .text-wrapper-50 {
  position: absolute;
  top: 4010px;
  left: 77px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-51 {
  position: absolute;
  top: 4008px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-52 {
  position: absolute;
  top: 4006px;
  left: 871px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-53 {
  top: 4110px;
  left: 77px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-54 {
  top: 4108px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-55 {
  top: 4108px;
  left: 871px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-56 {
  top: 4233px;
  left: 77px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-57 {
  position: absolute;
  top: 4231px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-58 {
  top: 4231px;
  left: 871px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-59 {
  position: absolute;
  top: 4356px;
  left: 81px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-60 {
  position: absolute;
  top: 4354px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-61 {
  position: absolute;
  top: 4354px;
  left: 875px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-62 {
  position: absolute;
  top: 4479px;
  left: 81px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .to-view-all-of-our {
  position: absolute;
  top: 4814px;
  left: 414px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-63 {
  position: absolute;
  top: 4479px;
  left: 478px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-64 {
  position: absolute;
  top: 4477px;
  left: 875px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .element-adults-infant {
  top: 4055px;
  left: 79px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-infant-2 {
  top: 4053px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .flexcontainer {
  width: 267px;
  top: 4053px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .text {
  position: relative;
  align-self: stretch;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #3dbb74;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 12.8px;
}

.travel-agency .text-wrapper-65 {
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #3dbb74;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 12.8px;
}

.travel-agency .flexcontainer-2 {
  width: 267px;
  top: 4153px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .flexcontainer-3 {
  width: 297px;
  top: 4276px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .flexcontainer-4 {
  width: 282px;
  top: 4399px;
  left: 875px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .flexcontainer-5 {
  width: 254px;
  top: 4528px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .element-adults-child-2 {
  top: 4155px;
  left: 81px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-2 {
  top: 4153px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-3 {
  top: 4278px;
  left: 79px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-4 {
  top: 4276px;
  left: 478px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-5 {
  top: 4401px;
  left: 81px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adult-children {
  top: 4399px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adult-child {
  top: 4524px;
  left: 81px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-teenager {
  top: 4521px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .TRIPMILESTONE-2 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 800;
  color: transparent;
  font-size: 26px;
  letter-spacing: 1.56px;
  line-height: normal;
}

.travel-agency .rectangle-10 {
  position: absolute;
  width: 166px;
  height: 51px;
  top: 4589px;
  left: 517px;
  border-radius: 26px;
  border: 3px solid;
  border-color: #3dbb74;
}

/* No Packages Message Styles */
.no-packages {
  text-align: center;
  padding: 3rem;
  color: var(--gray-600);
}

.no-packages h3 {
  margin-bottom: 1rem;
  color: var(--gray-800);
}

.no-packages-message {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin: 2rem auto;
  max-width: 600px;
}

.no-packages-icon {
  font-size: 4rem;
  color: var(--green);
  margin-bottom: 2rem;
}

.no-packages-message h3 {
  font-size: 1.5rem;
  color: var(--gray-800);
  margin-bottom: 1rem;
}

.no-packages-message p {
  color: var(--gray-600);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.no-packages-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.notify-btn, .explore-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notify-btn {
  background: var(--green);
  color: var(--white);
}

.notify-btn:hover {
  background: var(--green-dark);
  transform: translateY(-2px);
}

.explore-btn {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.explore-btn:hover {
  background: var(--gray-200);
  transform: translateY(-2px);
}

/* Test Button for Demo */
.demo-btn {
  background: var(--green);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-btn:hover {
  background: var(--secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Contact Details Modal - Enhanced */
.contact-details-modal {
  max-width: 650px;
  width: 90%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.contact-form-intro {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(21, 171, 139, 0.08) 0%, rgba(21, 171, 139, 0.03) 100%);
  border-radius: 16px;
  border: 1px solid rgba(21, 171, 139, 0.15);
  position: relative;
  overflow: hidden;
}

.contact-form-intro::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), #10b981, var(--primary));
  border-radius: 16px 16px 0 0;
}

.contact-form-intro h3 {
  color: var(--primary);
  font-weight: 700;
  font-size: 1.3rem;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.contact-form-intro h3 i {
  font-size: 1.5rem;
}

.contact-form-intro p {
  color: var(--text-clr);
  font-weight: 500;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 0.5rem;
}

.contact-form .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.contact-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
}

.contact-form .form-label {
  font-weight: 600;
  color: var(--text-clr);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.contact-form .form-label i {
  color: var(--primary);
  width: 18px;
  font-size: 1.1rem;
}

.contact-form .required {
  color: #ef4444;
  font-weight: 700;
  margin-left: 2px;
}

.contact-form .form-input {
  width: 100%;
  padding: 1.25rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 14px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
  font-family: inherit;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.contact-form .form-input::placeholder {
  color: #9ca3af;
  transition: opacity 0.3s ease;
}

.contact-form .form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(21, 171, 139, 0.12), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.contact-form .form-input:focus::placeholder {
  opacity: 0.7;
}

.contact-form .form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.12);
  animation: shake 0.5s ease-in-out;
}

.contact-form .form-input.success {
  border-color: #10b981;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.12);
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.error-message {
  color: #ef4444;
  font-size: 0.85rem;
  font-weight: 500;
  margin-top: 0.5rem;
  display: none;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(239, 68, 68, 0.05);
  border-radius: 8px;
  border-left: 3px solid #ef4444;
  animation: slideDown 0.3s ease-out;
}

.error-message.show {
  display: flex;
}

.error-message::before {
  content: '\f071';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  font-size: 0.8rem;
  color: #ef4444;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success message for form validation */
.success-message {
  color: #10b981;
  font-size: 0.85rem;
  font-weight: 500;
  margin-top: 0.5rem;
  display: none;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 8px;
  border-left: 3px solid #10b981;
  animation: slideDown 0.3s ease-out;
}

.success-message.show {
  display: flex;
}

.success-message::before {
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  font-size: 0.8rem;
  color: #10b981;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f1f5f9;
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  background: #f8fafc;
  color: var(--text-clr);
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-btn:hover {
  background: #f1f5f9;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.submit-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary) 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 14px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(21, 171, 139, 0.2);
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-btn:hover::before {
  left: 100%;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(21, 171, 139, 0.4);
}

.submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(21, 171, 139, 0.3);
}

.submit-btn:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn:disabled::before {
  display: none;
}

.submit-btn.loading {
  pointer-events: none;
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  cursor: wait;
}

.submit-btn.loading::before {
  display: none;
}

.submit-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-right: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.submit-btn.loading .btn-text {
  opacity: 0;
}

.submit-btn .btn-text {
  transition: opacity 0.3s ease;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Contact Modal Responsive Styles */
@media (max-width: 768px) {
  .contact-details-modal {
    width: 95%;
    max-width: none;
  }

  .contact-form .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 0.75rem;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .contact-details-modal {
    width: 98%;
    margin: 0.5rem;
  }

  .contact-form-intro {
    padding: 0.75rem;
    margin-bottom: 1.5rem;
  }

  .contact-form .form-label {
    font-size: 0.85rem;
  }

  .contact-form .form-input {
    padding: 0.875rem;
    font-size: 0.95rem;
  }

  .modal-body {
    padding: 1.5rem;
  }
}

/* Payment Modal Styles - Enhanced */
.payment-modal {
  max-width: 950px;
  width: 95%;
  max-height: 95vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
}

.payment-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, rgba(21, 171, 139, 0.05) 0%, rgba(21, 171, 139, 0.02) 100%);
  border-bottom: 1px solid rgba(21, 171, 139, 0.1);
  position: relative;
}

.payment-progress::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary), #10b981, var(--primary));
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.progress-step::after {
  content: '';
  position: absolute;
  top: 15px;
  left: 100%;
  width: 2rem;
  height: 2px;
  background: #e5e7eb;
  z-index: 1;
}

.progress-step:last-child::after {
  display: none;
}

.progress-step.completed::after,
.progress-step.active::after {
  background: var(--primary);
}

.progress-step i {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e5e7eb;
  color: #6b7280;
  font-size: 0.9rem;
  z-index: 2;
  position: relative;
}

.progress-step.completed i {
  background: var(--primary);
  color: white;
}

.progress-step.active i {
  background: var(--primary);
  color: white;
  animation: pulse 2s infinite;
}

.progress-step span {
  font-size: 0.8rem;
  font-weight: 500;
  color: #6b7280;
}

.progress-step.completed span,
.progress-step.active span {
  color: var(--primary);
  font-weight: 600;
}

.payment-body {
  padding: 1.5rem;
  max-height: 80vh;
  overflow-y: auto;
}

.payment-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.payment-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.payment-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.payment-section h3 i {
  color: var(--primary);
}

/* Package Summary - Enhanced */
.summary-card {
  background: linear-gradient(135deg, var(--primary) 0%, #059669 50%, var(--secondary) 100%);
  color: white;
  padding: 2rem;
  border-radius: 18px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 10px 30px rgba(21, 171, 139, 0.3);
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.summary-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.package-info h4 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 0.9rem;
  flex-direction: row !important;
  align-items: center !important;
}

.detail-item i {
  width: auto !important;
  min-width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline-flex !important;
  vertical-align: middle;
  opacity: 0.8;
}

.package-cost {
  text-align: right;
}

.cost-label {
  font-size: 0.9rem;
  opacity: 0.8;
  display: block;
}

.cost-amount {
  font-size: 1.8rem;
  font-weight: 800;
}

/* Customer Details */
.customer-card {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  gap: 0.5rem;
}

.info-label {
  font-weight: 600;
  color: var(--text-clr);
  min-width: 60px;
}

.info-value {
  color: var(--black);
}

.edit-details-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-details-btn:hover {
  background: var(--secondary);
}

/* EMI Plan - Enhanced */
.emi-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #bae6fd;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(186, 230, 253, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.emi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), #0ea5e9, var(--primary));
  border-radius: 16px 16px 0 0;
}

.emi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(186, 230, 253, 0.4);
}

.emi-summary {
  text-align: center;
  margin-bottom: 1rem;
}

.emi-amount {
  font-size: 2rem;
  font-weight: 800;
  color: var(--primary);
}

.period {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-clr);
}

.emi-details {
  font-size: 0.9rem;
  color: var(--text-clr);
  margin-top: 0.5rem;
}

.emi-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.breakdown-item.total {
  border-top: 2px solid #bae6fd;
  padding-top: 0.75rem;
  font-weight: 600;
  color: var(--primary);
}

/* Payment Schedule */
.schedule-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  cursor: pointer;
}

.toggle-schedule {
  background: none;
  border: none;
  color: var(--primary);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.toggle-schedule.expanded {
  transform: rotate(180deg);
}

.schedule-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.schedule-content.expanded {
  max-height: 300px;
}

/* Payment Methods */
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.payment-method::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(21, 171, 139, 0.05), transparent);
  transition: left 0.5s ease;
}

.payment-method:hover {
  border-color: var(--primary);
  background: rgba(21, 171, 139, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(21, 171, 139, 0.15);
}

.payment-method:hover::before {
  left: 100%;
}

.payment-method.active {
  border-color: var(--primary);
  background: rgba(21, 171, 139, 0.1);
}

.method-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.method-info {
  flex: 1;
}

.method-name {
  font-weight: 600;
  color: var(--black);
  display: block;
}

.method-desc {
  font-size: 0.85rem;
  color: var(--text-clr);
}

.method-logos {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.method-logos img {
  width: 30px;
  height: 20px;
  object-fit: contain;
}

.upi-logo,
.bank-logo {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--primary);
  background: rgba(21, 171, 139, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* Payment Forms */
.payment-form-container {
  margin-top: 1rem;
}

.payment-form-content {
  display: none;
}

.payment-form-content.active {
  display: block;
}

.payment-form-content h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.card-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  position: relative;
}

.form-group label {
  display: block;
  font-weight: 500;
  color: var(--text-clr);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary);
}

.card-icons {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 0.5rem;
}

.card-icons i {
  font-size: 1.2rem;
  color: #6b7280;
}

/* UPI Payment */
.upi-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.upi-option {
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.upi-option input[type="radio"] {
  margin-right: 0.5rem;
}

.upi-option label {
  font-weight: 500;
  cursor: pointer;
}

.upi-option input[type="text"] {
  width: 100%;
  margin-top: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
}

.qr-placeholder {
  width: 200px;
  height: 200px;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  margin-top: 0.5rem;
}

/* Net Banking */
.bank-selection select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
}

/* Security & Terms */
.security-badges {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.security-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  font-size: 0.85rem;
  color: #166534;
}

.security-badge i {
  color: #16a34a;
}

.terms-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.terms-checkbox input[type="checkbox"] {
  margin-top: 0.25rem;
}

.terms-checkbox label {
  font-size: 0.9rem;
  color: var(--text-clr);
  line-height: 1.4;
}

.terms-checkbox a {
  color: var(--primary);
  text-decoration: none;
}

.terms-checkbox a:hover {
  text-decoration: underline;
}

/* Payment Actions - Enhanced */
.payment-actions {
  display: flex;
  gap: 1rem;
  padding: 2rem;
  border-top: 1px solid rgba(21, 171, 139, 0.1);
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
}

.payment-actions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
}

.back-btn {
  flex: 1;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  border: none;
  border-radius: 14px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.2);
  position: relative;
  overflow: hidden;
}

.back-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.back-btn:hover::before {
  left: 100%;
}

.back-btn:hover {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(107, 114, 128, 0.3);
}

.pay-now-btn {
  flex: 2;
  padding: 1.25rem 2rem;
  background: linear-gradient(135deg, var(--primary) 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 14px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6px 20px rgba(21, 171, 139, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.pay-now-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.pay-now-btn:hover::before {
  left: 100%;
}

.pay-now-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(21, 171, 139, 0.4);
}

.pay-now-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(21, 171, 139, 0.3);
}

.pay-now-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(21, 171, 139, 0.2);
}

.pay-now-btn:disabled::before {
  display: none;
}

/* Payment Modal Responsive */
@media (max-width: 768px) {
  .payment-modal {
    width: 98%;
    max-height: 98vh;
  }

  .payment-progress {
    gap: 1rem;
  }

  .progress-step::after {
    width: 1rem;
  }

  .summary-card {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .customer-card {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .payment-actions {
    flex-direction: column;
  }

  .security-badges {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Payment Schedule Styles */
.schedule-list {
  padding: 1rem;
}

.schedule-item {
  display: grid;
  grid-template-columns: 120px 1fr auto;
  gap: 1rem;
  padding: 0.75rem;
  border-bottom: 1px solid #f1f5f9;
  align-items: center;
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-item.first-payment {
  background: rgba(21, 171, 139, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(21, 171, 139, 0.2);
}

.payment-date {
  font-weight: 600;
  color: var(--primary);
  font-size: 0.9rem;
}

.payment-description {
  color: var(--text-clr);
  font-size: 0.9rem;
}

.payment-amount {
  font-weight: 700;
  color: var(--black);
  text-align: right;
}

.first-payment .payment-amount {
  color: var(--primary);
}

/* Modal Animation */
.modal-overlay.show {
  animation: fadeIn 0.3s ease;
}

.modal-overlay.show .modal-content {
  animation: slideInUp 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
