<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TripXplo Family - Your Family Adventure, Planned & Paid Your Way</title>
    <meta name="description" content="Experience hassle-free family vacations with TripXplo's Family Prepaid EMI Packages. Discover, relax, and create lasting memories with flexible payment options." />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Volkhov:wght@400;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="globals.css" />
    <link rel="stylesheet" href="styleguide.css" />
    <link rel="stylesheet" href="style.css" />

    <!-- Supabase Client -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- html2canvas for card download/share -->
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>

    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <!-- Configuration -->
    <script src="js/config.js"></script>

    <!-- API Service -->
    <script src="js/apiService.js"></script>

    <!-- Database Integration -->
    <script src="js/databaseService.js"></script>

    <!-- Package Card Generator -->
    <script src="js/packageCardGenerator.js"></script>

    <!-- Preview PDF Function -->
    <script src="preview-pdf-function.js"></script>
  </head>
  <body>
    <div class="family-emi-website">
      <!-- Hero Section with Search Form -->
      <section class="hero-section">
        <!-- Navigation -->
        <nav class="navigation">
          <div class="nav-container">
            <div class="logo">
              <img src="https://tripemilestone.in-maa-1.linodeobjects.com/logo%2Ftripxplo-logo-crop.png" alt="TripXplo" class="logo-image" />
            </div>
            <div class="nav-links">
              <a href="#packages">Packages</a>
              <a href="#about">About</a>
              <a href="#contact">Contact</a>
              <button class="auth-btn" id="authBtn" onclick="openAuthModal()">
                <i class="fas fa-user"></i> Signup/Login
              </button>
            </div>
          </div>
        </nav>

        <!-- Hero Content -->
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">Your Family Adventure, Planned & Paid Your Way</h1>
            <p class="hero-subtitle">Enter your dream trip details and discover tailored EMI packages instantly</p>
          </div>

          <!-- Search Form -->
          <div class="search-form-container">
            <form class="search-form" id="familySearchForm">
              <div class="form-row">
                <!-- Destination Input -->
                <div class="input-group">
                  <label for="destination" class="input-label">
                    <i class="fas fa-map-marker-alt"></i> Where to?
                  </label>
                  <div class="destination-input-container">
                    <input
                      type="text"
                      id="destination"
                      name="destination"
                      placeholder="e.g., Kashmir, Goa, Manali"
                      class="form-input"
                      autocomplete="off"
                    />
                    <div id="destination-suggestions" class="destination-suggestions"></div>
                  </div>
                  <div class="destination-suggestions" id="destinationSuggestions"></div>
                </div>

                <!-- Travel Date Input -->
                <div class="input-group">
                <label for="travelMonth" class="input-label">
                <i class="fas fa-calendar-alt"></i> Select month and year
                </label>
               <input
                type="month"
                id="travelMonth"
                name="travelMonth"
                class="form-input"
                min="2024-12"
                onchange="handleMonthSelection()"
                />

                <!-- Optional Date Selector (appears after month selection) -->
                <div class="optional-date-selector" id="optionalDateSelector" style="display: none;">
                  <label class="optional-date-label">
                    <i class="fas fa-calendar-day"></i> Select specific date (optional)
                  </label>
                  <select id="specificDate" name="specificDate" class="form-input date-alternate">
                    <option value="">Any date in the month</option>
                  </select>
                </div>
                </div>

                <!-- Traveler Count Input -->
                <div class="input-group">
                  <label for="travelerSelector" class="input-label">
                    <i class="fas fa-users"></i> Who's going?
                  </label>
                  <div class="traveler-input-wrapper">
                    <button type="button" class="form-input traveler-selector" id="travelerSelector">
                      <span id="travelerDisplay">Stellar Duo</span>
                      <i class="fas fa-chevron-down"></i>
                    </button>
                    <button type="button" class="family-type-info-btn" onclick="openFamilyTypeInfoModal()" title="View family type information">
                      <i class="fas fa-info-circle"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Top Destinations Section -->
              <div class="top-destinations-section">
                <div class="top-destinations-header">
                  <span class="destinations-label">
                    <i class="fas fa-map-marked-alt"></i> Top Destinations:
                  </span>
                </div>
                <div class="destinations-list" id="topDestinationsList">
                  <!-- Loading placeholder -->
                  <div class="destinations-loading" id="destinationsLoading">
                    <div class="loading-spinner"></div>
                    <span>Loading destinations...</span>
                  </div>
                  <!-- Destinations will be loaded dynamically -->
                </div>
              </div>

              <!-- Search Button -->
              <button type="submit" class="search-btn">
                <i class="fas fa-sparkles"></i> Find My Trip
              </button>
            </form>

            <!-- Auto-detected Family Type Display -->
            <div class="family-type-display" id="familyTypeDisplay">
              <span class="family-type-label">Family Type:</span>
              <span class="family-type-name" id="detectedFamilyType">Stellar Duo (2 Adults)</span>
            </div>
          </div>
        </div>

        <!-- Hero Background Elements -->
        <div class="hero-background">
          <img class="hero-plane hero-plane-1" src="img/plane.png" alt="Plane" />
          <img class="hero-plane hero-plane-2" src="img/plane.png" alt="Plane" />
          <div class="hero-decoration"></div>
        </div>
      </section>
        <!-- Dynamic Results Section -->
        <section class="results-section" id="resultsSection" style="display: none;">
          <div class="results-container">
            <!-- Results Header -->
            <div class="results-header">
              <h2 class="results-title" id="resultsTitle">
                Showing packages for <span class="family-type-highlight">Family Nest (2 Adults, 2 Children)</span>
                to <span class="destination-highlight">Kashmir</span> in <span class="date-highlight">October 2025</span>
              </h2>
            </div>

            <!-- Package Cards Grid -->
            <div class="package-grid" id="packageGrid">
              <!-- Package Card 1 -->
              <div class="package-card">
                <div class="package-image">
                  <img src="img/rectangle-14.png" alt="Kashmir Winter Wonderland" />
                  <div class="duration-badge">5N / 6D</div>
                  <div class="offer-badge">
                    <i class="fas fa-gift"></i> 15% OFF
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">Kashmir Winter Wonderland</h3>

                  <div class="package-inclusions">
                    <div class="inclusion-item">
                      <i class="fas fa-plane"></i>
                      <span>Flights</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-hotel"></i>
                      <span>Hotels</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-utensils"></i>
                      <span>Meals</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-camera"></i>
                      <span>Sightseeing</span>
                    </div>
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹4,999<span class="emi-period">/month</span></div>
                    <div class="emi-details">for 8 Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹39,992</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('kashmir-winter')">
                    View Details
                  </button>
                </div>
              </div>

              <!-- Package Card 2 -->
              <div class="package-card">
                <div class="package-image">
                  <img src="img/rectangle-14.png" alt="Goa Beach Paradise" />
                  <div class="duration-badge">4N / 5D</div>
                  <div class="offer-badge early-bird">
                    <i class="fas fa-clock"></i> Early Bird
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">Goa Beach Paradise</h3>

                  <div class="package-inclusions">
                    <div class="inclusion-item">
                      <i class="fas fa-plane"></i>
                      <span>Flights</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-hotel"></i>
                      <span>Hotels</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-utensils"></i>
                      <span>Meals</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-umbrella-beach"></i>
                      <span>Beach Activities</span>
                    </div>
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹3,555<span class="emi-period">/month</span></div>
                    <div class="emi-details">for 9 Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹32,000</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('goa-beach')">
                    View Details
                  </button>
                </div>
              </div>

              <!-- Package Card 3 -->
              <div class="package-card">
                <div class="package-image">
                  <img src="img/rectangle-14.png" alt="Manali Adventure" />
                  <div class="duration-badge">3N / 4D</div>
                  <div class="offer-badge best-value">
                    <i class="fas fa-star"></i> Best Value
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">Manali Adventure</h3>

                  <div class="package-inclusions">
                    <div class="inclusion-item">
                      <i class="fas fa-plane"></i>
                      <span>Flights</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-hotel"></i>
                      <span>Hotels</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-utensils"></i>
                      <span>Meals</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-mountain"></i>
                      <span>Adventure</span>
                    </div>
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹2,799<span class="emi-period">/month</span></div>
                    <div class="emi-details">for 6 Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹16,794</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('manali-adventure')">
                    View Details
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Features Section -->
        <section class="features-section">
          <div class="features-container">
            <div class="features-header">
              <h2 class="features-title">Why Family EMI Packages</h2>
              <p class="features-subtitle">Make your dream vacation affordable with our monthly prepaid EMI plans</p>
            </div>

            <div class="features-grid">
              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-sync-alt"></i>
                </div>
                <h3 class="feature-title">Easy Plan Swap</h3>
                <p class="feature-description">
                  Swap plans 60 days before the travel date, change destinations or upgrade plans.
                </p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-calendar-check"></i>
                </div>
                <h3 class="feature-title">No Last Minute Rush</h3>
                <p class="feature-description">
                  Enjoy your family vacation without the stress of peak season crowds.
                </p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="feature-title">Guaranteed Availability</h3>
                <p class="feature-description">
                  Secure your booking without worrying about last-minute availability issues.
                </p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-medal"></i>
                </div>
                <h3 class="feature-title">Rewards on Booking</h3>
                <p class="feature-description">
                  Earn benefits on each EMI payment and by referring friends or family.
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- Traveler Selector Modal -->
        <div class="modal-overlay" id="travelerModal">
          <div class="modal-content traveler-modal">
            <div class="modal-header">
              <h3>Select Travelers</h3>
              <button class="modal-close" onclick="closeTravelerModal()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="modal-body">
              <div class="traveler-counters">
                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Adults</span>
                    <span class="counter-sublabel">(12+ years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('adults', -1)">-</button>
                    <span class="counter-value" id="adultsCount">2</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('adults', 1)">+</button>
                  </div>
                </div>

                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Child below 5</span>
                    <span class="counter-sublabel">(2-5 years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('child', -1)">-</button>
                    <span class="counter-value" id="childCount">0</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('child', 1)">+</button>
                  </div>
                </div>

                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Children</span>
                    <span class="counter-sublabel">(6-11 years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('children', -1)">-</button>
                    <span class="counter-value" id="childrenCount">0</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('children', 1)">+</button>
                  </div>
                </div>

                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Infants</span>
                    <span class="counter-sublabel">(Under 2 years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('infants', -1)">-</button>
                    <span class="counter-value" id="infantsCount">0</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('infants', 1)">+</button>
                  </div>
                </div>
              </div>

              <div class="detected-family-type">
                <span class="family-type-label">Detected Family Type:</span>
                <span class="family-type-value" id="modalFamilyType">Stellar Duo (2 Adults)</span>
              </div>
            </div>

            <div class="modal-footer">
              <button class="apply-btn" onclick="applyTravelerSelection()">
                Apply Selection
              </button>
            </div>
          </div>
        </div>

        <!-- Package Details Modal -->
        <div class="modal-overlay" id="packageModal">
          <div class="modal-content package-modal">
            <div class="modal-header">
              <h3 id="packageModalTitle">Kashmir Winter Wonderland</h3>
              <button class="modal-close" onclick="closePackageModal()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="modal-body">
              <!-- Image Gallery -->
              <div class="image-gallery">
                <div class="main-image">
                  <img id="packageMainImage" src="img/rectangle-14.png" alt="Package Image" />
                </div>
              </div>

              <!-- Package Tabs -->
              <div class="package-tabs">
                <button class="tab-btn active" onclick="switchTab('overview')">Overview</button>
                <button class="tab-btn" onclick="switchTab('emi-options')">EMI Options</button>
                <button class="tab-btn" onclick="switchTab('itinerary')">Itinerary</button>
              </div>

              <!-- Tab Content -->
              <div class="tab-content">
                <!-- Overview Tab -->
                <div class="tab-pane active" id="overview">
                  <div class="package-overview">
                    <div class="overview-item">
                      <strong>Duration:</strong> 5 Nights / 6 Days
                    </div>
                    <div class="overview-item">
                      <strong>Highlights:</strong> Dal Lake, Gulmarg, Pahalgam, Srinagar
                    </div>
                    <div class="overview-item">
                      <strong>Inclusions:</strong>
                      <ul>
                        <li>Round-trip flights</li>
                        <li>4-star hotel accommodation</li>
                        <li>Daily breakfast and dinner</li>
                        <li>All sightseeing and transfers</li>
                        <li>Professional guide</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- EMI Options Tab -->
                <div class="tab-pane" id="emi-options">
                  <div class="emi-plans">
                    <div class="emi-plan">
                      <div class="plan-header">
                        <h4>3 Months</h4>
                        <span class="plan-label quick-pay">Quick Pay</span>
                      </div>
                      <div class="plan-amount">₹13,997/month</div>
                      <div class="plan-details">
                        <div>Total: ₹41,991</div>
                        <div>Processing Fee: ₹999</div>
                      </div>
                      <button class="select-plan-btn">Select Plan</button>
                    </div>

                    <div class="emi-plan best-value">
                      <div class="plan-header">
                        <h4>6 Months</h4>
                        <span class="plan-label">Best Value</span>
                      </div>
                      <div class="plan-amount">₹6,998/month</div>
                      <div class="plan-details">
                        <div>Total: ₹41,988</div>
                        <div>Processing Fee: ₹996</div>
                      </div>
                      <button class="select-plan-btn">Select Plan</button>
                    </div>

                    <div class="emi-plan">
                      <div class="plan-header">
                        <h4>12 Months</h4>
                        <span class="plan-label low-monthly">Low Monthly</span>
                      </div>
                      <div class="plan-amount">₹3,499/month</div>
                      <div class="plan-details">
                        <div>Total: ₹41,988</div>
                        <div>Processing Fee: ₹996</div>
                      </div>
                      <button class="select-plan-btn">Select Plan</button>
                    </div>
                  </div>
                </div>

                <!-- Itinerary Tab -->
                <div class="tab-pane" id="itinerary">
                  <div class="itinerary-content">
                    <div class="day-item">
                      <h4>Day 1: Arrival in Srinagar</h4>
                      <p>Arrive at Srinagar airport, transfer to houseboat, Dal Lake shikara ride</p>
                    </div>
                    <div class="day-item">
                      <h4>Day 2: Srinagar to Gulmarg</h4>
                      <p>Drive to Gulmarg, Gondola ride, snow activities</p>
                    </div>
                    <div class="day-item">
                      <h4>Day 3: Gulmarg to Pahalgam</h4>
                      <p>Transfer to Pahalgam, Betaab Valley, Aru Valley visit</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Family Type Info Modal -->
        <div class="modal-overlay" id="familyTypeInfoModal">
          <div class="modal-content family-type-info-modal" id="familyTypeModalContent">
            <!-- Scroll Indicator -->
            <div class="scroll-indicator" id="scrollIndicator">
              <div class="scroll-dot active"></div>
              <div class="scroll-dot"></div>
              <div class="scroll-dot"></div>
            </div>

            <div class="modal-header">
              <h3><i class="fas fa-users"></i> Available Family Type</h3>
              <div class="modal-header-actions">
                <button class="print-btn" onclick="printFamilyTypePDF()" title="Print Family Types">
                  <i class="fas fa-print"></i>
                </button>
                <button class="modal-close" onclick="closeFamilyTypeInfoModal()">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>

                <!-- Loading State -->
                <div class="family-type-loading" id="familyTypeLoading">
                  <div class="loading-spinner"></div>
                  <span>Loading family type data...</span>
                </div>

                <!-- Family Type Data Table -->
                <div class="family-type-table-container" id="familyTypeTableContainer" style="display: none;">
                  <div class="table-responsive">
                    <table class="family-type-table">
                      <thead>
                        <tr>
                          <th>Family Type Name</th>
                          <th>Adults</th>
                          <th>Children (6-11)</th>
                          <th>Child (2-5)</th>
                          <th>Infants (0-2)</th>
                          <th>Total Count</th>
                        </tr>
                      </thead>
                      <tbody id="familyTypeTableBody">
                        <!-- Data will be populated dynamically -->
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Error State -->
                <div class="family-type-error" id="familyTypeError" style="display: none;">
                  <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <h4>Unable to load family type data</h4>
                  <p>There was an error loading the family type information from the database. Please try again later.</p>
                  <button class="retry-btn" onclick="loadFamilyTypeData()">
                    <i class="fas fa-redo"></i> Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Details Modal -->
        <div class="modal-overlay" id="contactDetailsModal">
          <div class="modal-content contact-details-modal">
            <div class="modal-header">
              <h3><i class="fas fa-user-edit"></i> Contact Details</h3>
              <button class="modal-close" onclick="closeContactDetailsModal()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="modal-body">
              <div class="contact-form-intro">
                <h3><i class="fas fa-user-circle"></i> Contact Details</h3>
                <p>Please provide your contact details to proceed with the booking. We'll use this information to confirm your travel package and send important updates.</p>
              </div>

              <form id="contactDetailsForm" class="contact-form">
                <div class="form-row">
                  <div class="form-group">
                    <label for="firstName" class="form-label">
                      <i class="fas fa-user"></i> First Name <span class="required">*</span>
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      class="form-input"
                      placeholder="Enter your first name"
                      required
                      autocomplete="given-name"
                    />
                    <div class="error-message" id="firstNameError"></div>
                  </div>

                  <div class="form-group">
                    <label for="lastName" class="form-label">
                      <i class="fas fa-user"></i> Last Name <span class="required">*</span>
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      class="form-input"
                      placeholder="Enter your last name"
                      required
                      autocomplete="family-name"
                    />
                    <div class="error-message" id="lastNameError"></div>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="mobileNumber" class="form-label">
                      <i class="fas fa-phone"></i> Mobile Number <span class="required">*</span>
                    </label>
                    <input
                      type="tel"
                      id="mobileNumber"
                      name="mobileNumber"
                      class="form-input"
                      placeholder="Enter your mobile number"
                      required
                      autocomplete="tel"
                      pattern="[0-9]{10}"
                      maxlength="10"
                    />
                    <div class="error-message" id="mobileNumberError"></div>
                  </div>

                  <div class="form-group">
                    <label for="emailId" class="form-label">
                      <i class="fas fa-envelope"></i> Email ID <span class="required">*</span>
                    </label>
                    <input
                      type="email"
                      id="emailId"
                      name="emailId"
                      class="form-input"
                      placeholder="Enter your email address"
                      required
                      autocomplete="email"
                    />
                    <div class="error-message" id="emailIdError"></div>
                  </div>
                </div>

                <div class="form-actions">
                  <button type="button" class="cancel-btn" onclick="closeContactDetailsModal()">
                    <i class="fas fa-times"></i> Cancel
                  </button>
                  <button type="submit" class="submit-btn">
                    <span class="btn-text">
                      <i class="fas fa-check"></i> Submit & Continue
                    </span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Authentication Modal -->
        <div class="modal-overlay" id="authModal">
          <div class="modal-content auth-modal">
            <div class="modal-header">
              <h3 id="authModalTitle">
                <i class="fas fa-mobile-alt"></i> Login to TripXplo
              </h3>
              <button class="modal-close" onclick="closeAuthModal()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="modal-body">
              <!-- Login Step -->
              <div class="auth-step" id="loginStep">
                <form id="loginForm" class="auth-form">
                  <div class="form-group">
                    <label for="loginMobileNumber" class="form-label">
                      <i class="fas fa-phone"></i> Mobile Number
                    </label>
                    <div class="mobile-input-container">
                      <span class="country-code">+91</span>
                      <input
                        type="tel"
                        id="loginMobileNumber"
                        name="mobileNumber"
                        class="form-input mobile-input"
                        placeholder="Enter 10-digit mobile number"
                        maxlength="10"
                        pattern="[0-9]{10}"
                        required
                      />
                    </div>
                    <div class="error-message" id="loginMobileError"></div>
                  </div>

                  <div class="form-group">
                    <label for="loginPin" class="form-label">
                      <i class="fas fa-lock"></i> 4-Digit PIN
                    </label>
                    <div class="pin-input-container">
                      <input
                        type="password"
                        id="loginPin"
                        name="pin"
                        class="form-input pin-input"
                        placeholder="Enter PIN"
                        maxlength="4"
                        pattern="[0-9]{4}"
                        required
                      />
                      <button type="button" class="toggle-pin-visibility" onclick="toggleLoginPinVisibility()">
                        <i class="fas fa-eye" id="loginPinVisibilityIcon"></i>
                      </button>
                    </div>
                    <div class="error-message" id="loginPinError"></div>
                  </div>

                  <div class="form-actions">
                    <button type="submit" class="submit-btn">
                      <span class="btn-text">
                        <i class="fas fa-sign-in-alt"></i> Login
                      </span>
                    </button>
                  </div>

                  <div class="auth-switch">
                    <p>New user? <button type="button" class="switch-btn" onclick="switchToSignup()">Register</button></p>
                  </div>
                </form>
              </div>

              <!-- Signup Step 1: User Details -->
              <div class="auth-step" id="signupStep1" style="display: none;">
                <form id="signupForm1" class="auth-form">
                  <div class="form-group">
                    <label for="signupName" class="form-label">
                      <i class="fas fa-user"></i> Full Name
                    </label>
                    <input
                      type="text"
                      id="signupName"
                      name="name"
                      class="form-input"
                      placeholder="Enter your full name"
                      required
                    />
                    <div class="error-message" id="signupNameError"></div>
                  </div>

                  <div class="form-group">
                    <label for="signupEmail" class="form-label">
                      <i class="fas fa-envelope"></i> Email Address
                    </label>
                    <input
                      type="email"
                      id="signupEmail"
                      name="email"
                      class="form-input"
                      placeholder="Enter your email address"
                      required
                    />
                    <div class="error-message" id="signupEmailError"></div>
                  </div>

                  <div class="form-group">
                    <label for="signupMobileNumber" class="form-label">
                      <i class="fas fa-phone"></i> Mobile Number
                    </label>
                    <div class="mobile-input-container">
                      <span class="country-code">+91</span>
                      <input
                        type="tel"
                        id="signupMobileNumber"
                        name="mobileNumber"
                        class="form-input mobile-input"
                        placeholder="Enter 10-digit mobile number"
                        maxlength="10"
                        pattern="[0-9]{10}"
                        required
                      />
                    </div>
                    <div class="error-message" id="signupMobileError"></div>
                  </div>

                  <div class="form-actions">
                    <button type="submit" class="submit-btn">
                      <span class="btn-text">
                        <i class="fas fa-arrow-right"></i> Continue
                      </span>
                    </button>
                  </div>

                  <div class="auth-switch">
                    <p>Already have an account? <button type="button" class="switch-btn" onclick="switchToLogin()">Login</button></p>
                  </div>
                </form>
              </div>

              <!-- Signup Step 2: PIN Setup -->
              <div class="auth-step" id="signupStep2" style="display: none;">
                <div class="auth-form-intro">
                  <p>Create your 4-digit PIN for secure login</p>
                  <div class="user-display">
                    <i class="fas fa-user-circle"></i>
                    <span id="displayUserInfo">John Doe - <EMAIL></span>
                    <button class="change-details-btn" onclick="goBackToSignupDetails()">
                      <i class="fas fa-edit"></i> Change
                    </button>
                  </div>
                </div>

                <form id="signupForm2" class="auth-form">
                  <div class="form-group">
                    <label for="signupPin" class="form-label">
                      <i class="fas fa-lock"></i> Create 4-Digit PIN
                    </label>
                    <div class="pin-input-container">
                      <input
                        type="password"
                        id="signupPin"
                        name="pin"
                        class="form-input pin-input"
                        placeholder="Create PIN"
                        maxlength="4"
                        pattern="[0-9]{4}"
                        required
                      />
                      <button type="button" class="toggle-pin-visibility" onclick="toggleSignupPinVisibility()">
                        <i class="fas fa-eye" id="signupPinVisibilityIcon"></i>
                      </button>
                    </div>
                    <div class="error-message" id="signupPinError"></div>
                  </div>

                  <div class="form-group">
                    <label for="signupConfirmPin" class="form-label">
                      <i class="fas fa-lock"></i> Confirm PIN
                    </label>
                    <div class="pin-input-container">
                      <input
                        type="password"
                        id="signupConfirmPin"
                        name="confirmPin"
                        class="form-input pin-input"
                        placeholder="Re-enter PIN"
                        maxlength="4"
                        pattern="[0-9]{4}"
                        required
                      />
                    </div>
                    <div class="error-message" id="signupConfirmPinError"></div>
                  </div>

                  <div class="form-actions">
                    <button type="button" class="back-btn" onclick="goBackToSignupDetails()">
                      <i class="fas fa-arrow-left"></i> Back
                    </button>
                    <button type="submit" class="submit-btn">
                      <span class="btn-text">
                        <i class="fas fa-user-plus"></i> Create Account
                      </span>
                    </button>
                  </div>
                </form>
              </div>

              <!-- Loading State -->
              <div class="auth-loading" id="authLoading" style="display: none;">
                <div class="loading-spinner"></div>
                <p id="loadingMessage">Processing your request...</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Modal -->
        <div class="modal-overlay" id="paymentModal">
          <div class="modal-content payment-modal">
            <div class="modal-header">
              <div class="payment-progress">
                <div class="progress-step completed">
                  <i class="fas fa-check"></i>
                  <span>Contact Details</span>
                </div>
                <div class="progress-step active">
                  <i class="fas fa-credit-card"></i>
                  <span>Payment</span>
                </div>
                <div class="progress-step">
                  <i class="fas fa-check-circle"></i>
                  <span>Confirmation</span>
                </div>
              </div>
              <button class="modal-close" onclick="closePaymentModal()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="modal-body payment-body">
              <!-- Package Summary Section -->
              <div class="payment-section package-summary">
                <h3><i class="fas fa-suitcase-rolling"></i> Package Summary</h3>
                <div class="summary-card">
                  <div class="package-info">
                    <h4 id="paymentPackageTitle">Kashmir Winter Wonderland</h4>
                    <div class="package-details">
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span id="paymentDestination">Kashmir</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-calendar"></i>
                        <span id="paymentDuration">5 Nights / 6 Days</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-users"></i>
                        <span id="paymentTravelers">2 Adults</span>
                      </div>
                    </div>
                  </div>
                  <div class="package-cost">
                    <div class="total-cost">
                      <span class="cost-label">Total Cost</span>
                      <span class="cost-amount" id="paymentTotalCost">₹45,000</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Customer Details Section -->
              <div class="payment-section customer-details">
                <h3><i class="fas fa-user"></i> Customer Details</h3>
                <div class="customer-card">
                  <div class="customer-info">
                    <div class="info-item">
                      <span class="info-label">Name:</span>
                      <span class="info-value" id="paymentCustomerName">Arunpandian C</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">Email:</span>
                      <span class="info-value" id="paymentCustomerEmail"><EMAIL></span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">Phone:</span>
                      <span class="info-value" id="paymentCustomerPhone">+91 9159325225</span>
                    </div>
                  </div>
                  <button class="edit-details-btn" onclick="editCustomerDetails()">
                    <i class="fas fa-edit"></i> Edit Details
                  </button>
                </div>
              </div>

              <!-- EMI Plan Section -->
              <div class="payment-section emi-plan">
                <h3><i class="fas fa-calendar-alt"></i> Selected EMI Plan</h3>
                <div class="emi-card">
                  <div class="emi-summary">
                    <div class="emi-amount">
                      <span class="amount" id="paymentEmiAmount">₹7,500</span>
                      <span class="period">/month</span>
                    </div>
                    <div class="emi-details">
                      <span id="paymentEmiMonths">6</span> months EMI plan
                    </div>
                  </div>
                  <div class="emi-breakdown">
                    <div class="breakdown-item">
                      <span>Monthly EMI:</span>
                      <span id="paymentMonthlyEmi">₹7,500</span>
                    </div>
                    <div class="breakdown-item">
                      <span>Processing Fee:</span>
                      <span id="paymentProcessingFee">₹500</span>
                    </div>
                    <div class="breakdown-item total">
                      <span>Total Amount:</span>
                      <span id="paymentTotalAmount">₹45,500</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Payment Schedule -->
              <div class="payment-section payment-schedule">
                <h3><i class="fas fa-list"></i> Payment Schedule</h3>
                <div class="schedule-card">
                  <div class="schedule-header">
                    <span>EMI Schedule</span>
                    <button class="toggle-schedule" onclick="togglePaymentSchedule()">
                      <i class="fas fa-chevron-down"></i>
                    </button>
                  </div>
                  <div class="schedule-content" id="paymentScheduleContent">
                    <!-- Schedule will be populated dynamically -->
                  </div>
                </div>
              </div>

              <!-- Payment Options -->
              <div class="payment-section payment-options">
                <h3><i class="fas fa-credit-card"></i> Payment Options</h3>
                <div class="payment-methods">
                  <div class="payment-method active" data-method="card">
                    <div class="method-icon">
                      <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="method-info">
                      <span class="method-name">Credit/Debit Card</span>
                      <span class="method-desc">Visa, Mastercard, RuPay</span>
                    </div>
                    <div class="method-logos">
                      <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/visa/visa-original.svg" alt="Visa" />
                      <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mastercard/mastercard-original.svg" alt="Mastercard" />
                    </div>
                  </div>

                  <div class="payment-method" data-method="upi">
                    <div class="method-icon">
                      <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="method-info">
                      <span class="method-name">UPI</span>
                      <span class="method-desc">Pay using UPI ID or QR code</span>
                    </div>
                    <div class="method-logos">
                      <span class="upi-logo">UPI</span>
                    </div>
                  </div>

                  <div class="payment-method" data-method="netbanking">
                    <div class="method-icon">
                      <i class="fas fa-university"></i>
                    </div>
                    <div class="method-info">
                      <span class="method-name">Net Banking</span>
                      <span class="method-desc">All major banks supported</span>
                    </div>
                    <div class="method-logos">
                      <span class="bank-logo">Banks</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Payment Form -->
              <div class="payment-section payment-form">
                <div class="payment-form-container">
                  <!-- Card Payment Form -->
                  <div class="payment-form-content active" id="cardPaymentForm">
                    <h4>Card Details</h4>
                    <form class="card-form">
                      <div class="form-group">
                        <label for="cardNumber">Card Number</label>
                        <input type="text" id="cardNumber" placeholder="1234 5678 9012 3456" maxlength="19" />
                        <div class="card-icons">
                          <i class="fab fa-cc-visa"></i>
                          <i class="fab fa-cc-mastercard"></i>
                        </div>
                      </div>
                      <div class="form-row">
                        <div class="form-group">
                          <label for="expiryDate">Expiry Date</label>
                          <input type="text" id="expiryDate" placeholder="MM/YY" maxlength="5" />
                        </div>
                        <div class="form-group">
                          <label for="cvv">CVV</label>
                          <input type="text" id="cvv" placeholder="123" maxlength="4" />
                        </div>
                      </div>
                      <div class="form-group">
                        <label for="cardholderName">Cardholder Name</label>
                        <input type="text" id="cardholderName" placeholder="Name as on card" />
                      </div>
                    </form>
                  </div>

                  <!-- UPI Payment Form -->
                  <div class="payment-form-content" id="upiPaymentForm">
                    <h4>UPI Payment</h4>
                    <div class="upi-options">
                      <div class="upi-option">
                        <input type="radio" id="upiId" name="upiMethod" value="upiId" checked />
                        <label for="upiId">Pay using UPI ID</label>
                        <input type="text" id="upiIdInput" placeholder="yourname@paytm" />
                      </div>
                      <div class="upi-option">
                        <input type="radio" id="upiQr" name="upiMethod" value="qr" />
                        <label for="upiQr">Scan QR Code</label>
                        <div class="qr-placeholder">QR Code will appear here</div>
                      </div>
                    </div>
                  </div>

                  <!-- Net Banking Form -->
                  <div class="payment-form-content" id="netbankingPaymentForm">
                    <h4>Select Your Bank</h4>
                    <div class="bank-selection">
                      <select id="bankSelect">
                        <option value="">Select your bank</option>
                        <option value="sbi">State Bank of India</option>
                        <option value="hdfc">HDFC Bank</option>
                        <option value="icici">ICICI Bank</option>
                        <option value="axis">Axis Bank</option>
                        <option value="kotak">Kotak Mahindra Bank</option>
                        <option value="pnb">Punjab National Bank</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Security & Terms -->
              <div class="payment-section security-terms">
                <div class="security-badges">
                  <div class="security-badge">
                    <i class="fas fa-shield-alt"></i>
                    <span>256-bit SSL Encrypted</span>
                  </div>
                  <div class="security-badge">
                    <i class="fas fa-lock"></i>
                    <span>PCI DSS Compliant</span>
                  </div>
                </div>
                <div class="terms-checkbox">
                  <input type="checkbox" id="agreeTerms" required />
                  <label for="agreeTerms">
                    I agree to the <a href="#" onclick="showTermsModal()">Terms & Conditions</a>
                    and <a href="#" onclick="showPrivacyModal()">Privacy Policy</a>
                  </label>
                </div>
              </div>

              <!-- Payment Actions -->
              <div class="payment-actions">
                <button class="back-btn" onclick="editCustomerDetails()">
                  <i class="fas fa-arrow-left"></i> Back to Edit Details
                </button>
                <button class="pay-now-btn" onclick="processPayment()">
                  <i class="fas fa-lock"></i> Pay Now - <span id="payNowAmount">₹7,500</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- JavaScript -->
        <script>
          // Test script execution
          console.log('🚀 Main script loaded');

          // Make functions globally accessible - defined after DOM is ready
          document.addEventListener('DOMContentLoaded', function() {
            window.openAuthModal = function() {
              console.log('openAuthModal called');
              const authModal = document.getElementById('authModal');
              if (authModal) {
                authModal.style.display = 'flex';
                if (typeof resetAuthModal === 'function') {
                  resetAuthModal();
                } else {
                  console.warn('resetAuthModal function not found');
                }
              } else {
                console.error('authModal element not found');
              }
            };

            // Profile and booking functions will be made globally accessible after they're defined

            console.log('✅ Global functions registered');
          });

          // Global data storage
          let familyTypesData = [];
          let destinationsData = [];
          let travelers = { adults: 2, child: 0, children: 0, infants: 0 };
          let currentPackages = [];

          // Use the notification manager from apiService.js
          // It's available as window.notificationManager

          // Authentication state
          let authState = {
            isAuthenticated: false,
            user: null,
            currentStep: 'login', // 'login', 'signup1', 'signup2'
            signupData: {
              name: '',
              email: '',
              mobileNumber: ''
            }
          };

          // API Configuration - Dynamic URL detection
          const API_CONFIG = {
            getBaseUrl: () => {
              const hostname = window.location.hostname;
              const protocol = window.location.protocol;

              // Check if running on localhost
              if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('localhost')) {
                return 'http://localhost:3001';
              }

              // Check if running on file:// protocol (local HTML file)
              if (protocol === 'file:') {
                return 'http://localhost:3001';
              }

              // Production environment
              return 'https://family.tripxplo.com';
            },

            getApiUrl: (endpoint) => {
              return `${API_CONFIG.getBaseUrl()}/api/${endpoint}`;
            }
          };

          // Log current environment
          console.log('🌐 Environment detected:', {
            hostname: window.location.hostname,
            protocol: window.location.protocol,
            apiBaseUrl: API_CONFIG.getBaseUrl()
          });

          // Initialize the page
          document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Initializing Family EMI Website...');

            try {
              // Load initial data
              await loadInitialData();

              // Setup UI components
              setupDestinationAutocomplete();
              setupFormSubmission();
              updateFamilyTypeDisplay();

              // Load top destinations
              await loadTopDestinations();

              // Check for test payment modal trigger
              const urlParams = new URLSearchParams(window.location.search);
              if (urlParams.get('test_payment') === 'true') {
                const testData = sessionStorage.getItem('tripxplo_payment_data');
                if (testData) {
                  setTimeout(() => {
                    openPaymentModal(JSON.parse(testData));
                  }, 1000);
                }
              }

              console.log('✅ Website initialized successfully');
            } catch (error) {
              console.error('❌ Error initializing website:', error);
              notificationManager.show('Failed to load initial data. Some features may not work properly.', 'warning');
            }
          });

          // Load initial data from database
          async function loadInitialData() {
            try {
              console.log('🔄 Loading initial data from live database...');

              // Load family types and destinations in parallel using direct database connection
              const [familyTypesResponse, destinationsResponse] = await Promise.all([
                databaseService.getFamilyTypes(),
                databaseService.getDestinations()
              ]);

              if (familyTypesResponse.success) {
                familyTypesData = familyTypesResponse.data;
                console.log('📊 Loaded family types from CRM DB:', familyTypesData.length);
              }

              if (destinationsResponse.success) {
                destinationsData = destinationsResponse.data;
                console.log('🗺️ Loaded destinations from Quote DB:', destinationsData.length);

                // Initialize destination autocomplete
                initializeDestinationAutocomplete(destinationsData);
              }

              // Show success notification
              if (familyTypesData.length > 0 && destinationsData.length > 0) {
                notificationManager.show(`✅ Loaded ${familyTypesData.length} family types and ${destinationsData.length} destinations`, 'success', 3000);
              }

            } catch (error) {
              console.error('❌ Error loading initial data:', error);
              notificationManager.show('⚠️ Using offline data. Some features may be limited.', 'warning');

              // Fallback to static data
              familyTypesData = [
                { family_id: 'SD', family_type: 'Stellar Duo', composition: '2 Adults' },
                { family_id: 'BB', family_type: 'Baby Bliss', composition: '2 Adults + 1 Infant' },
                { family_id: 'TD', family_type: 'Tiny Delight', composition: '2 Adults + 1 Child' }
              ];
              destinationsData = [
                { destination: 'Kashmir' }, { destination: 'Goa' }, { destination: 'Manali' }
              ];
            }
          }

          // Setup destination autocomplete
          function setupDestinationAutocomplete() {
            const destinationInput = document.getElementById('destination');
            const suggestionsDiv = document.getElementById('destinationSuggestions');

            destinationInput.addEventListener('input', function() {
              const value = this.value.toLowerCase();
              const filtered = destinationsData.filter(dest =>
                dest.destination.toLowerCase().includes(value)
              );

              if (value && filtered.length > 0) {
                suggestionsDiv.innerHTML = filtered.map(dest =>
                  `<div class="suggestion-item" onclick="selectDestination('${dest.destination}')">${dest.destination}</div>`
                ).join('');
                suggestionsDiv.style.display = 'block';
              } else {
                suggestionsDiv.style.display = 'none';
              }
            });
          }

          // Select destination from suggestions
          function selectDestination(destination) {
            document.getElementById('destination').value = destination;
            document.getElementById('destinationSuggestions').style.display = 'none';
          }

          // Load top destinations from database
          async function loadTopDestinations() {
            const destinationsList = document.getElementById('topDestinationsList');
            const loadingElement = document.getElementById('destinationsLoading');

            try {
              console.log('🗺️ Loading top destinations from database...');
              
              // Get destinations from database
              const response = await databaseService.getDestinations();
              
              if (response.success && response.data && response.data.length > 0) {
                // Take top 10 destinations and format them
                const topDestinations = response.data.slice(0, 10).map(dest => {
                  const name = typeof dest === 'string' ? dest : dest.destination || dest.name;
                  return {
                    name: toTitleCase(name),
                    icon: getDestinationIcon(name.toLowerCase()),
                    originalName: name
                  };
                });

                // Hide loading
                loadingElement.style.display = 'none';

                // Create destination chips
                const destinationsHTML = topDestinations.map(dest => `
                  <button type="button" 
                          class="destination-chip" 
                          data-destination="${dest.originalName.toLowerCase()}"
                          onclick="selectTopDestination('${dest.originalName}')"
                          title="Explore ${dest.name}">
                    <i class="${dest.icon}"></i>
                    ${dest.name}
                  </button>
                `).join('');

                destinationsList.innerHTML = destinationsHTML;

                // Add staggered animation
                const chips = destinationsList.querySelectorAll('.destination-chip');
                chips.forEach((chip, index) => {
                  chip.style.opacity = '0';
                  chip.style.transform = 'translateY(20px)';
                  setTimeout(() => {
                    chip.style.transition = 'all 0.4s ease';
                    chip.style.opacity = '1';
                    chip.style.transform = 'translateY(0)';
                  }, index * 100);
                });

                console.log('✅ Loaded top destinations:', topDestinations.length);
              } else {
                // Fallback to popular destinations
                loadFallbackDestinations();
              }
            } catch (error) {
              console.error('❌ Error loading destinations:', error);
              loadFallbackDestinations();
            }
          }

          // Load fallback destinations if database fails
          function loadFallbackDestinations() {
            const destinationsList = document.getElementById('topDestinationsList');
            const loadingElement = document.getElementById('destinationsLoading');

            const fallbackDestinations = [
              { name: 'Goa', icon: 'fas fa-umbrella-beach' },
              { name: 'Andaman', icon: 'fas fa-water' },
              { name: 'Munnar', icon: 'fas fa-mountain' },
              { name: 'Alleppey', icon: 'fas fa-tree' },
              { name: 'Kashmir', icon: 'fas fa-snowflake' },
              { name: 'Manali', icon: 'fas fa-mountain' },
              { name: 'Shimla', icon: 'fas fa-mountain' },
              { name: 'Rajasthan', icon: 'fas fa-fort-awesome' },
              { name: 'Kerala', icon: 'fas fa-tree' },
              { name: 'Agra', icon: 'fas fa-monument' }
            ];

            loadingElement.style.display = 'none';

            const destinationsHTML = fallbackDestinations.map(dest => `
              <button type="button" 
                      class="destination-chip" 
                      data-destination="${dest.name.toLowerCase()}"
                      onclick="selectTopDestination('${dest.name}')"
                      title="Explore ${dest.name}">
                <i class="${dest.icon}"></i>
                ${dest.name}
              </button>
            `).join('');

            destinationsList.innerHTML = destinationsHTML;

            // Add staggered animation
            const chips = destinationsList.querySelectorAll('.destination-chip');
            chips.forEach((chip, index) => {
              chip.style.opacity = '0';
              chip.style.transform = 'translateY(20px)';
              setTimeout(() => {
                chip.style.transition = 'all 0.4s ease';
                chip.style.opacity = '1';
                chip.style.transform = 'translateY(0)';
              }, index * 100);
            });

            console.log('✅ Loaded fallback destinations');
          }

          // Convert string to title case
          function toTitleCase(str) {
            return str.toLowerCase().split(' ').map(word => {
              return word.charAt(0).toUpperCase() + word.slice(1);
            }).join(' ');
          }

          // Get appropriate icon for destination
          function getDestinationIcon(destination) {
            const iconMap = {
              'goa': 'fas fa-umbrella-beach',
              'andaman': 'fas fa-water',
              'munnar': 'fas fa-mountain',
              'alleppey': 'fas fa-tree',
              'kerala': 'fas fa-tree',
              'kashmir': 'fas fa-snowflake',
              'manali': 'fas fa-mountain',
              'shimla': 'fas fa-mountain',
              'rajasthan': 'fas fa-fort-awesome',
              'agra': 'fas fa-monument',
              'delhi': 'fas fa-city',
              'mumbai': 'fas fa-building',
              'bangalore': 'fas fa-city',
              'chennai': 'fas fa-city',
              'kolkata': 'fas fa-city',
              'hyderabad': 'fas fa-city',
              'pune': 'fas fa-city',
              'jaipur': 'fas fa-fort-awesome',
              'udaipur': 'fas fa-fort-awesome',
              'jodhpur': 'fas fa-fort-awesome'
            };

            // Check for partial matches
            for (const [key, icon] of Object.entries(iconMap)) {
              if (destination.includes(key)) {
                return icon;
              }
            }

            // Default icon
            return 'fas fa-map-marker-alt';
          }

          // Select destination from top destinations
          function selectTopDestination(destination) {
            const destinationInput = document.getElementById('destination');
            destinationInput.value = destination;
            destinationInput.focus();

            // Hide any open suggestions
            document.getElementById('destinationSuggestions').style.display = 'none';

            // Add a subtle animation to indicate selection
            destinationInput.style.borderColor = 'var(--primary)';
            destinationInput.style.boxShadow = '0 0 0 3px rgba(21, 171, 139, 0.1)';

            // Reset the styling after a short delay
            setTimeout(() => {
              destinationInput.style.borderColor = '#e5e7eb';
              destinationInput.style.boxShadow = 'none';
            }, 1000);

            // Add success notification
            notificationManager.show(`Selected ${toTitleCase(destination)} as destination`, 'success');
          }

          // Handle month selection and show optional date selector
          function handleMonthSelection() {
            const monthInput = document.getElementById('travelMonth');
            const dateSelector = document.getElementById('optionalDateSelector');
            const specificDateSelect = document.getElementById('specificDate');

            if (monthInput.value) {
              // Parse the selected month and year
              const [year, month] = monthInput.value.split('-');
              const selectedDate = new Date(year, month - 1, 1);
              const daysInMonth = new Date(year, month, 0).getDate();

              // Clear previous options
              specificDateSelect.innerHTML = '<option value="">Any date in the month</option>';

              // Add date options for the selected month
              for (let day = 1; day <= daysInMonth; day++) {
                const dateOption = new Date(year, month - 1, day);
                const today = new Date();

                // Only show dates from today onwards
                if (dateOption >= today.setHours(0, 0, 0, 0)) {
                  const option = document.createElement('option');
                  option.value = `${year}-${month.padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

                  // Format the date nicely
                  const dayName = dateOption.toLocaleDateString('en-US', { weekday: 'short' });
                  const dayNum = day.toString().padStart(2, '0');
                  option.textContent = `${dayName}, ${dayNum}`;

                  specificDateSelect.appendChild(option);
                }
              }

              // Show the date selector with a smooth animation
              dateSelector.style.display = 'block';
              dateSelector.style.opacity = '0';
              dateSelector.style.transform = 'translateY(-10px)';

              setTimeout(() => {
                dateSelector.style.transition = 'all 0.3s ease';
                dateSelector.style.opacity = '1';
                dateSelector.style.transform = 'translateY(0)';
              }, 10);

            } else {
              // Hide the date selector if no month is selected
              dateSelector.style.display = 'none';
            }
          }

          // Hide suggestions when clicking outside
          document.addEventListener('click', function(event) {
            if (!event.target.closest('.input-group')) {
              document.getElementById('destinationSuggestions').style.display = 'none';
            }
          });

          // Setup form submission
          function setupFormSubmission() {
            document.getElementById('familySearchForm').addEventListener('submit', function(e) {
              e.preventDefault();
              searchPackages();
            });

            document.getElementById('travelerSelector').addEventListener('click', function() {
              openTravelerModal();
            });
          }

          // Search packages
          async function searchPackages() {
            const destination = document.getElementById('destination').value;
            const travelMonth = document.getElementById('travelMonth').value;
            const specificDate = document.getElementById('specificDate').value;

            if (!destination || !travelMonth) {
              notificationManager.show('Please fill in destination and travel month', 'warning');
              return;
            }

            // Use specific date if selected, otherwise use the first day of the month
            const travelDate = specificDate || `${travelMonth}-01`;

            try {
              // Prepare search parameters
              const searchParams = {
                destination,
                travel_date: travelDate,
                adults: travelers.adults,
                child: travelers.child,
                children: travelers.children,
                infants: travelers.infants
              };

              console.log('🔍 Searching packages with params:', searchParams);

              // Call database service to search packages directly
              const response = await databaseService.searchPackages(searchParams);

              if (response.success) {
                currentPackages = response.packages;

                if (response.packages && response.packages.length > 0) {
                  displaySearchResults(response);
                  notificationManager.show(`Found ${response.packages.length} packages for ${destination}`, 'success');
                } else {
                  displayNoPackagesMessage(destination, response.matched_family_type);
                }

                // Show results section
                const resultsSection = document.getElementById('resultsSection');
                resultsSection.style.display = 'block';
                resultsSection.classList.add('show');
                resultsSection.scrollIntoView({ behavior: 'smooth' });
              } else {
                displayNoPackagesMessage(destination);
                notificationManager.show('Error searching packages: ' + response.error, 'error');
              }
            } catch (error) {
              console.error('Error searching packages:', error);
              displayNoPackagesMessage(destination);
              notificationManager.show(error.message || 'Failed to search packages. Please try again.', 'error');
            }
          }

          // Display no packages available message
          function displayNoPackagesMessage(destination, familyType) {
            const resultsSection = document.getElementById('resultsSection');
            const packageGrid = document.getElementById('packageGrid');
            const resultsTitle = document.getElementById('resultsTitle');

            // Update title
            const familyTypeText = familyType ? familyType.family_type : 'your family';
            resultsTitle.innerHTML = `
              No packages available for <span class="family-type-highlight">${familyTypeText}</span>
              to <span class="destination-highlight">${destination}</span>
            `;

            // Display no packages message
            packageGrid.innerHTML = `
              <div class="no-packages-message">
                <div class="no-packages-icon">
                  <i class="fas fa-search"></i>
                </div>
                <h3>No packages available right now</h3>
                <p>We're working on adding more packages for ${destination}. We'll update you soon!</p>
                <div class="no-packages-actions">
                  <button class="notify-btn" onclick="requestNotification('${destination}')">
                    <i class="fas fa-bell"></i> Notify me when available
                  </button>
                  <button class="explore-btn" onclick="exploreOtherDestinations()">
                    <i class="fas fa-map"></i> Explore other destinations
                  </button>
                </div>
              </div>
            `;

            // Show results section
            resultsSection.style.display = 'block';
            resultsSection.classList.add('show');
            resultsSection.scrollIntoView({ behavior: 'smooth' });
          }

          // Request notification for destination
          function requestNotification(destination) {
            // You can implement this to collect email for notifications
            notificationManager.show(`We'll notify you when packages for ${destination} are available!`, 'success');
          }

          // Explore other destinations
          function exploreOtherDestinations() {
            // Clear destination input and focus
            document.getElementById('destination').value = '';
            document.getElementById('destination').focus();

            // Hide results section
            document.getElementById('resultsSection').style.display = 'none';
          }

          // Display search results
          function displaySearchResults(response) {
            const { matched_family_type, packages, search_params } = response;

            // Update results header
            let dateObj;
            let monthYear = 'Invalid Date';

            try {
              // Handle different date formats
              if (search_params.travel_date) {
                // If travel_date is already in YYYY-MM-DD format, use it directly
                if (search_params.travel_date.includes('-') && search_params.travel_date.length >= 7) {
                  dateObj = new Date(search_params.travel_date);
                } else {
                  // If it's in YYYY-MM format, append -01
                  dateObj = new Date(search_params.travel_date + '-01');
                }

                // Check if date is valid
                if (!isNaN(dateObj.getTime())) {
                  monthYear = dateObj.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
                }
              }
            } catch (error) {
              console.error('Error formatting date:', error);
              monthYear = 'Selected Date';
            }

            document.getElementById('resultsTitle').innerHTML = `
              Showing packages for <span class="family-type-highlight">${matched_family_type.family_type}</span>
              to <span class="destination-highlight">${search_params.destination}</span> in <span class="date-highlight">${monthYear}</span>
            `;

            // Update package grid
            const packageGrid = document.getElementById('packageGrid');
            if (packages.length === 0) {
              packageGrid.innerHTML = `
                <div class="no-packages">
                  <h3>No packages found</h3>
                  <p>Try searching for a different destination or travel date.</p>
                </div>
              `;
              return;
            }

            packageGrid.innerHTML = packages.map(pkg => createPackageCard(pkg)).join('');
          }

          // Create package card HTML
          function createPackageCard(pkg) {
            // Ensure EMI options exist and have valid data
            const emiOptions = pkg.emi_options || [];
            // Check for selected EMI first, then featured, then first option
            const selectedEMI = emiOptions.find(emi => emi.selected) ||
                               emiOptions.find(emi => emi.is_featured) ||
                               emiOptions[0] || {
              monthly_amount: Math.round((pkg.total_price || 45000) / 6),
              months: 6,
              total_amount: pkg.total_price || 45000
            };

            // Ensure all required properties exist
            const safePackage = {
              id: pkg.id || 'unknown',
              title: pkg.title || 'Travel Package',
              duration_days: pkg.duration_days || 5,
              offer_badge: pkg.offer_badge || 'Special Offer',
              images: pkg.images || ['img/rectangle-14.png'],
              inclusions: pkg.inclusions || ['Flights', 'Hotels', 'Meals']
            };

            // Use online image for package card (same as modal)
            const onlineImageUrl = getOnlineImageUrl(pkg.quote_name || pkg.package_name || pkg.title, pkg.destination);

            return `
              <div class="package-card">
                <div class="package-image">
                  <img src="${onlineImageUrl}" alt="${safePackage.title}" />
                  <div class="duration-badge">${safePackage.duration_days}</div>
                  <div class="offer-badge">
                    <i class="fas fa-gift"></i> ${safePackage.offer_badge}
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">${pkg.quote_name || pkg.package_name || pkg.title || 'Travel Package'}</h3>

                  <div class="package-inclusions">
                    ${safePackage.inclusions.map(inc => `
                      <div class="inclusion-item">
                        <i class="fas fa-${getInclusionIcon(inc)}"></i>
                        <span>${inc}</span>
                      </div>
                    `).join('')}
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹${(selectedEMI.monthly_amount || 0).toLocaleString()}<span class="emi-period">/month</span></div>
                    <div class="emi-details">for ${selectedEMI.months || 6} ${selectedEMI.is_custom ? 'Custom ' : ''}Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹${(selectedEMI.total_amount || 0).toLocaleString()}</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('${safePackage.id}')">
                    View Details
                  </button>
                </div>
              </div>
            `;
          }

          // Get icon for inclusion type
          function getInclusionIcon(inclusion) {
            const iconMap = {
              'Flights': 'plane',
              'Hotels': 'hotel',
              'Meals': 'utensils',
              'Sightseeing': 'camera',
              'Beach Activities': 'umbrella-beach',
              'Adventure': 'mountain'
            };
            return iconMap[inclusion] || 'check';
          }

          // Detect family type based on traveler counts
          function detectFamilyType() {
            const { adults, child, children, infants } = travelers;

            if (familyTypesData.length === 0) {
              return {
                family_type: 'Custom Family',
                composition: `${adults} Adults${child > 0 ? ` + ${child} Child (2-5 yrs)` : ''}${children > 0 ? ` + ${children} Children (6-11 yrs)` : ''}${infants > 0 ? ` + ${infants} Infants` : ''}`
              };
            }

            // Find exact match first
            let match = familyTypesData.find(ft =>
              ft.no_of_adults === adults &&
              ft.no_of_child === child &&
              ft.no_of_children === children &&
              ft.no_of_infants === infants
            );

            // If no exact match, find closest match
            if (!match) {
              match = familyTypesData.find(ft =>
                ft.no_of_adults === adults &&
                ft.no_of_child >= child &&
                ft.no_of_children >= children &&
                ft.no_of_infants >= infants
              );
            }

            // Default to first family type if no match
            if (!match) {
              match = familyTypesData[0] || {
                family_type: 'Custom Family',
                composition: `${adults} Adults${child > 0 ? ` + ${child} Child (2-5 yrs)` : ''}${children > 0 ? ` + ${children} Children (6-11 yrs)` : ''}${infants > 0 ? ` + ${infants} Infants` : ''}`
              };
            }

            return match;
          }

          // Extract only the family type name from database format
          function extractFamilyTypeName(fullFamilyType) {
            if (!fullFamilyType) return 'Family Package';

            // Database format: "Stellar Duo - 2 Adults + 1 Child (2 to 5 yrs) + 1 Children (5 to 11 yrs)"
            // We want only: "Stellar Duo"
            const parts = fullFamilyType.split(' - ');
            return parts[0].trim();
          }

          // Update family type display
          function updateFamilyTypeDisplay() {
            const familyType = detectFamilyType();
            document.getElementById('detectedFamilyType').textContent = familyType.family_type;

            // Update traveler display with ONLY family type name (no composition details)
            const familyTypeName = extractFamilyTypeName(familyType.family_type);
            document.getElementById('travelerDisplay').textContent = familyTypeName;

            if (document.getElementById('modalFamilyType')) {
              document.getElementById('modalFamilyType').textContent = familyType.family_type;
            }
          }

          // Traveler Modal Functions
          function openTravelerModal() {
            document.getElementById('travelerModal').style.display = 'flex';
          }

          function closeTravelerModal() {
            document.getElementById('travelerModal').style.display = 'none';
          }

          function updateCounter(type, change) {
            travelers[type] = Math.max(0, travelers[type] + change);
            if (type === 'adults') travelers[type] = Math.max(1, travelers[type]); // At least 1 adult

            document.getElementById(type + 'Count').textContent = travelers[type];
            updateFamilyTypeDisplay();
          }

          function applyTravelerSelection() {
            updateFamilyTypeDisplay();
            closeTravelerModal();
          }

          // Package Modal Functions
          async function openPackageModal(packageId) {
            // Set current package ID for card generation
            currentPackageId = packageId;

            const modal = document.getElementById('packageModal');
            modal.style.display = 'flex';

            try {

              // Show loading state with better UX
              document.getElementById('packageModalTitle').textContent = 'Loading Package Details...';

              // Add loading state to tab content instead of replacing entire modal body
              const tabContent = modal.querySelector('.tab-content');
              if (tabContent) {
                tabContent.innerHTML = `
                  <div class="package-loading-state">
                    <div class="loading-spinner"></div>
                    <p>Fetching package information and EMI options...</p>
                  </div>
                `;
              }

              // Load package details from database
              const response = await databaseService.getPackageDetails(packageId);

              if (response.success) {
                populatePackageModal(response.package);
              } else {
                // Show error state in tab content instead of closing
                const tabContent = modal.querySelector('.tab-content');
                if (tabContent) {
                  tabContent.innerHTML = `
                    <div class="package-error-state">
                      <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                      </div>
                      <h4>Package Not Found</h4>
                      <p>The requested package could not be loaded. Please try again or contact support.</p>
                      <button class="retry-btn" onclick="openPackageModal('${packageId}')">
                        <i class="fas fa-redo"></i> Retry
                      </button>
                    </div>
                  `;
                }
                document.getElementById('packageModalTitle').textContent = 'Error Loading Package';
              }
            } catch (error) {
              console.error('Error loading package details:', error);
              // Show error state in tab content instead of closing
              const tabContent = modal.querySelector('.tab-content');
              if (tabContent) {
                tabContent.innerHTML = `
                  <div class="package-error-state">
                    <div class="error-icon">
                      <i class="fas fa-wifi"></i>
                    </div>
                    <h4>Connection Error</h4>
                    <p>Failed to load package details. Please check your connection and try again.</p>
                    <button class="retry-btn" onclick="openPackageModal('${packageId}')">
                      <i class="fas fa-redo"></i> Retry
                    </button>
                  </div>
                `;
              }
              document.getElementById('packageModalTitle').textContent = 'Connection Error';
            }
          }

          // Get online image URL based on package title/destination
          function getOnlineImageUrl(packageTitle, destination) {
            // Extract destination or use package title for image search
            const searchTerm = destination || packageTitle || 'travel destination';
            const cleanTerm = searchTerm.toLowerCase().replace(/[^a-z\s]/g, '').trim();

            // Map common destinations to trending open source images
            const imageMap = {
              'andaman': 'https://images.unsplash.com/photo-1640718835374-6116a99c6e6c?w=800&h=400&fit=crop',
              'coorg': 'https://images.unsplash.com/photo-1602216056096-3b40cc0c9944?w=800&h=400&fit=crop',
              'goa': 'https://images.unsplash.com/photo-1512343879784-a960bf40e7f2?w=800&h=400&fit=crop',
              'manali': 'https://images.unsplash.com/photo-1626621341517-bbf3d9990a23?w=800&h=400&fit=crop',
              'munnar': 'https://images.unsplash.com/photo-1602216056096-3b40cc0c9944?w=800&h=400&fit=crop',
              'varkala': 'https://images.unsplash.com/photo-1697193375091-f9bfe3d7ed7b?w=800&h=400&fit=crop'
            };

            // Find matching destination
            for (const [key, url] of Object.entries(imageMap)) {
              if (cleanTerm.includes(key)) {
                return url;
              }
            }

            // Default fallback image
            return 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800&h=400&fit=crop';
          }

          // Populate package modal with data
          function populatePackageModal(pkg) {
            // Update title and image
            document.getElementById('packageModalTitle').textContent = pkg.quote_name || pkg.package_name || pkg.title || 'Travel Package';

            // Use online image based on package title/destination
            const onlineImageUrl = getOnlineImageUrl(pkg.quote_name || pkg.package_name || pkg.title, pkg.destination);
            const mainImageElement = document.getElementById('packageMainImage');
            if (mainImageElement) {
              mainImageElement.src = onlineImageUrl;
            }

            // First, restore the tab content structure
            const modal = document.getElementById('packageModal');
            const tabContent = modal.querySelector('.tab-content');
            if (tabContent) {
              tabContent.innerHTML = `
                <!-- Overview Tab -->
                <div class="tab-pane active" id="overview">
                  <!-- Content will be populated below -->
                </div>
                <!-- EMI Options Tab -->
                <div class="tab-pane" id="emi-options">
                  <!-- Content will be populated below -->
                </div>
                <!-- Itinerary Tab -->
                <div class="tab-pane" id="itinerary">
                  <div class="itinerary-content">
                    <p>Detailed itinerary will be provided upon booking confirmation.</p>
                  </div>
                </div>
              `;
            }

            // Update overview tab with enhanced package details
            const overviewTab = document.getElementById('overview');
            overviewTab.innerHTML = `
              <div class="package-overview">
                <!-- Package Summary Card -->
                <div class="package-summary-card">
                  <div class="summary-header">
                    <h4>${pkg.quote_name || pkg.package_name || pkg.title || 'Travel Package'}</h4>
                    <div class="package-badges">
                      ${pkg.category ? `<span class="category-badge">${pkg.category}</span>` : ''}
                    </div>
                  </div>

                  <div class="summary-details">
                    <div class="detail-row">
                      <div class="detail-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span><strong>Duration:</strong> ${pkg.nights || pkg.duration_days}</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span><strong>Destination:</strong> ${pkg.destination || 'Travel Destination'}</span>
                      </div>
                    </div>

                    <div class="detail-row">
                      <div class="detail-item" style="grid-column: 1 / -1;">
                        <i class="fas fa-hotel"></i>
                        <span><strong>Hotels:</strong></span>
                        <div style="margin-top: 8px;">
                          ${pkg.hotels_list && pkg.hotels_list.length > 0 ?
                            pkg.hotels_list.map(hotel => {
                              const nights = hotel.nights || hotel.stay_nights || 1;
                              const hotelName = hotel.hotel_name || 'Hotel Included';
                              let mealPlan = hotel.meal_plan || 'Breakfast included';

                              // Convert meal plan codes to descriptions
                              mealPlan = getMealPlanDescription(mealPlan);

                              return `<div style="padding: 4px 0;  border-left: 3px solid #00e499; padding-left: 8px; margin: 4px 0;">${nights}N - ${hotelName} (${mealPlan})</div>`;
                            }).join('') :
                            `<div>${pkg.hotel_name || 'Hotel Included'} (${pkg.hotel_category || 'Standard'})</div>`
                          }
                        </div>
                      </div>
                    </div>

                    <div class="detail-row">
                      <div class="detail-item">
                        <i class="fas fa-users"></i>
                        <span><strong>Family Type:</strong> ${pkg.family_type || 'Family Package'}</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-rupee-sign"></i>
                        <span><strong>Total Price:</strong> ₹${(pkg.total_price || 0).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Package Description -->
                ${pkg.description ? `
                <div class="package-description">
                  <h5><i class="fas fa-info-circle"></i> About This Package</h5>
                  <p>${pkg.description}</p>
                </div>
                ` : ''}

                <!-- Inclusions Section -->
                <div class="package-inclusions">
                  <h5><i class="fas fa-check-circle text-success"></i> What's Included</h5>
                  <div class="inclusions-grid">
                    ${(pkg.inclusions || ['Accommodation', 'Transfers']).map(inc => `
                      <div class="inclusion-item">
                        <i class="fas fa-check text-success"></i>
                        <span>${inc}</span>
                      </div>
                    `).join('')}
                  </div>
                </div>

                <!-- Exclusions Section -->
                <div class="package-exclusions">
                  <h5><i class="fas fa-times-circle text-danger"></i> What's Not Included</h5>
                  <div class="exclusions-grid">
                    ${(pkg.exclusions || ['Personal expenses', 'Travel insurance', 'Items not mentioned in inclusions']).slice(0, 5).map(exc => `
                      <div class="exclusion-item">
                        <i class="fas fa-times text-danger"></i>
                        <span>${exc}</span>
                      </div>
                    `).join('')}
                  </div>
                </div>

                <!-- View EMI Option Button -->
                <div style="display: flex; justify-content: center; margin: 0;">
                  <button class="view-emi-btn" onclick="switchTab('emi-options')">View EMI Option</button>
                </div>

                <!-- Additional Information -->
                <div class="additional-info">
                  <div class="info-grid">
                    ${pkg.ferry_included ? `
                    <div class="info-item">
                      <i class="fas fa-ship text-primary"></i>
                      <span>Ferry Included</span>
                    </div>
                    ` : ''}
                    ${pkg.guide_included ? `
                    <div class="info-item">
                      <i class="fas fa-user-tie text-primary"></i>
                      <span>Professional Guide</span>
                    </div>
                    ` : ''}
                    ${pkg.activities_included && pkg.activities_included.length > 0 ? `
                    <div class="info-item">
                      <i class="fas fa-hiking text-primary"></i>
                      <span>Activities Included</span>
                    </div>
                    ` : ''}
                  </div>
                </div>

                <!-- Package Validity -->
                ${pkg.validity ? `
                <div class="package-validity">
                  <i class="fas fa-calendar-check"></i>
                  <span>${pkg.validity}</span>
                </div>
                ` : ''}
              </div>
            `;

            // Update itinerary tab with enhanced layout
            const itineraryTab = document.getElementById('itinerary');
            if (pkg.itinerary && pkg.itinerary.length > 0) {
              itineraryTab.innerHTML = `
                <div class="itinerary-content">
                  <div class="itinerary-header">
                    <h4><i class="fas fa-route"></i> Detailed Itinerary</h4>
                    <p>Day-wise breakdown of your ${pkg.nights || pkg.duration_days || 5}-night journey to ${pkg.destination}</p>
                  </div>

                  <div class="itinerary-timeline">
                    ${pkg.itinerary.map((day, index) => `
                      <div class="day-item ${index === 0 ? 'first-day' : ''} ${index === pkg.itinerary.length - 1 ? 'last-day' : ''}">
                        <div class="day-marker">
                          <div class="day-number">${day.day}</div>
                          <div class="day-line"></div>
                        </div>
                        <div class="day-content">
                          <div class="day-header">
                            <h5>Day ${day.day}: ${day.title}</h5>
                            ${index === 0 ? '<span class="day-badge arrival">Arrival</span>' : ''}
                            ${index === pkg.itinerary.length - 1 ? '<span class="day-badge departure">Departure</span>' : ''}
                          </div>
                          <p class="day-description">${day.description}</p>
                          ${day.highlights ? `
                          <div class="day-highlights">
                            <strong>Highlights:</strong>
                            <ul>
                              ${day.highlights.map(highlight => `<li>${highlight}</li>`).join('')}
                            </ul>
                          </div>
                          ` : ''}
                        </div>
                      </div>
                    `).join('')}
                  </div>

                  <!-- Activities Section -->
                  ${pkg.activities_included && pkg.activities_included.length > 0 ? `
                  <div class="included-activities">
                    <h5><i class="fas fa-star"></i> Special Activities Included</h5>
                    <div class="activities-grid">
                      ${pkg.activities_included.map(activity => `
                        <div class="activity-item">
                          <i class="fas fa-check-circle"></i>
                          <span>${activity}</span>
                        </div>
                      `).join('')}
                    </div>
                  </div>
                  ` : ''}

                  <!-- Important Notes -->
                  <div class="itinerary-notes">
                    <h6><i class="fas fa-info-circle"></i> Important Notes</h6>
                    <ul>
                      <li>Itinerary is subject to change based on weather conditions and local circumstances</li>
                      <li>Check-in time is usually 2:00 PM and check-out time is 12:00 PM</li>
                      <li>All timings are approximate and may vary based on traffic and other factors</li>
                      ${pkg.ferry_included ? '<li>Ferry timings are subject to weather conditions</li>' : ''}
                    </ul>
                  </div>
                </div>
              `;
            } else {
              // Generate basic itinerary based on duration
              const days = pkg.nights || pkg.duration_days || 5;
              const destination = pkg.destination || 'your destination';
              itineraryTab.innerHTML = `
                <div class="itinerary-content">
                  <div class="itinerary-header">
                    <h4><i class="fas fa-route"></i> Itinerary Overview</h4>
                    <p>Your ${days}-night journey to ${destination}</p>
                  </div>

                  <div class="itinerary-timeline">
                    <div class="day-item first-day">
                      <div class="day-marker">
                        <div class="day-number">1</div>
                        <div class="day-line"></div>
                      </div>
                      <div class="day-content">
                        <div class="day-header">
                          <h5>Day 1: Arrival in ${destination}</h5>
                          <span class="day-badge arrival">Arrival</span>
                        </div>
                        <p class="day-description">Arrive at ${destination}, meet and greet by our representative, transfer to hotel and check-in. Rest of the day at leisure.</p>
                      </div>
                    </div>

                    ${Array.from({length: days - 1}, (_, i) => `
                      <div class="day-item">
                        <div class="day-marker">
                          <div class="day-number">${i + 2}</div>
                          <div class="day-line"></div>
                        </div>
                        <div class="day-content">
                          <div class="day-header">
                            <h5>Day ${i + 2}: ${destination} Sightseeing</h5>
                          </div>
                          <p class="day-description">Explore the beautiful attractions and local culture of ${destination}. Visit major tourist spots and enjoy local experiences.</p>
                        </div>
                      </div>
                    `).join('')}

                    <div class="day-item last-day">
                      <div class="day-marker">
                        <div class="day-number">${days + 1}</div>
                        <div class="day-line"></div>
                      </div>
                      <div class="day-content">
                        <div class="day-header">
                          <h5>Day ${days + 1}: Departure</h5>
                          <span class="day-badge departure">Departure</span>
                        </div>
                        <p class="day-description">After breakfast, check-out from hotel and transfer to airport/railway station for onward journey.</p>
                      </div>
                    </div>
                  </div>

                  <div class="itinerary-notes">
                    <h6><i class="fas fa-info-circle"></i> Important Notes</h6>
                    <ul>
                      <li>Detailed itinerary will be provided upon booking confirmation</li>
                      <li>Itinerary is subject to change based on weather conditions</li>
                      <li>All timings are approximate and may vary</li>
                    </ul>
                  </div>
                </div>
              `;
            }

            // Update EMI options tab
            const emiTab = document.getElementById('emi-options');
            const emiOptions = pkg.emi_options || [];

            // Show loading state first
            emiTab.innerHTML = `
              <div class="emi-loading-state">
                <div class="loading-spinner"></div>
                <p>Loading EMI options...</p>
              </div>
            `;

            // Simulate brief loading for better UX
            setTimeout(() => {
              if (emiOptions.length === 0) {
                emiTab.innerHTML = `
                  <div class="emi-plans">
                    <div class="no-emi-plans">
                      <h3>EMI Options Coming Soon</h3>
                      <p>Please contact us for flexible payment options.</p>
                      <button class="select-plan-btn" onclick="selectEMIPlan('${pkg.id}', 'contact')">
                        Contact for EMI
                      </button>
                    </div>
                  </div>
                `;
              } else {
              // Filter to show only 3 and 6 month options, then add custom option
              const fixedEmiOptions = emiOptions.filter(emi => emi.months === 3 || emi.months === 6);
              const packageTotalPrice = pkg.total_price || pkg.price || 45000;

              emiTab.innerHTML = `
                <div class="emi-plans">
                  ${fixedEmiOptions.map(emi => `
                    <div class="emi-plan ${emi.is_featured ? 'best-value' : ''}">
                      <div class="plan-header">
                        <h4>${emi.months || 6} Months</h4>
                        <span class="plan-label ${(emi.label || 'standard').toLowerCase().replace(' ', '-')}">${emi.label || 'Standard'}</span>
                      </div>
                      <div class="plan-amount">₹${(emi.monthly_amount || 0).toLocaleString()}/month</div>
                      <div class="plan-details">
                        <div>Total: ₹${(emi.total_amount || 0).toLocaleString()}</div>
                        <div class="prepaid-note">Prepaid EMI - No Processing Fee</div>
                      </div>
                      <button class="select-plan-btn" onclick="selectEMIPlan('${pkg.id}', '${emi.id || 'default'}')">
                        Select Plan
                      </button>
                    </div>
                  `).join('')}

                  <!-- Custom EMI Option -->
                  <div class="emi-plan custom-emi-plan">
                    <div class="plan-header">
                      <h4>Custom EMI</h4>
                      <span class="plan-label customize">Customize</span>
                    </div>
                    <div class="custom-emi-selector">
                      <label for="customEmiMonths-${pkg.id}">Choose Duration:</label>
                      <select id="customEmiMonths-${pkg.id}" class="custom-emi-dropdown" onchange="updateCustomEMI('${pkg.id}', ${packageTotalPrice})">
                        <option value="">Select months</option>
                        ${Array.from({length: 17}, (_, i) => i + 2).map(month => `
                          <option value="${month}">${month} Months</option>
                        `).join('')}
                      </select>
                    </div>
                    <div class="plan-amount" id="customEmiAmount-${pkg.id}">Select duration</div>
                    <div class="plan-details">
                      <div id="customEmiTotal-${pkg.id}">Total: ₹${packageTotalPrice.toLocaleString()}</div>
                      <div class="prepaid-note">Prepaid EMI - No Processing Fee</div>
                    </div>
                    <button class="select-plan-btn" id="customEmiBtn-${pkg.id}" onclick="selectCustomEMI('${pkg.id}')" disabled>
                      Select Custom Plan
                    </button>
                  </div>
                </div>
              `;
              }
            }, 300); // Brief loading delay for better UX
          }

          // Global variable to track selected EMI plans for each package
          let selectedEMIPlans = {};

          // Global variable to track custom EMI selections
          let customEMISelections = {};

          // Update custom EMI calculation
          function updateCustomEMI(packageId, totalPrice) {
            const monthsSelect = document.getElementById(`customEmiMonths-${packageId}`);
            const amountElement = document.getElementById(`customEmiAmount-${packageId}`);
            const totalElement = document.getElementById(`customEmiTotal-${packageId}`);
            const selectBtn = document.getElementById(`customEmiBtn-${packageId}`);

            const selectedMonths = parseInt(monthsSelect.value);

            if (selectedMonths && selectedMonths >= 2 && selectedMonths <= 18) {
              const monthlyAmount = Math.round(totalPrice / selectedMonths);

              // Store custom EMI selection
              customEMISelections[packageId] = {
                months: selectedMonths,
                monthly_amount: monthlyAmount,
                total_amount: totalPrice,
                processing_fee: 0,
                label: 'Custom',
                is_custom: true
              };

              amountElement.textContent = `₹${monthlyAmount.toLocaleString()}/month`;
              totalElement.textContent = `Total: ₹${totalPrice.toLocaleString()}`;
              selectBtn.disabled = false;
              selectBtn.textContent = `Select ${selectedMonths} Month Plan`;
            } else {
              amountElement.textContent = 'Select duration';
              totalElement.textContent = `Total: ₹${totalPrice.toLocaleString()}`;
              selectBtn.disabled = true;
              selectBtn.textContent = 'Select Custom Plan';
              delete customEMISelections[packageId];
            }
          }

          // Select custom EMI plan
          function selectCustomEMI(packageId) {
            const customEmi = customEMISelections[packageId];
            if (!customEmi) return;

            // Store the custom EMI as selected
            selectedEMIPlans[packageId] = 'custom';

            // Update package data with custom EMI
            const pkg = currentPackages.find(p => p.id === packageId);
            if (pkg) {
              // Add custom EMI to options if not already present
              if (!pkg.emi_options) pkg.emi_options = [];

              // Remove any existing custom EMI
              pkg.emi_options = pkg.emi_options.filter(emi => !emi.is_custom);

              // Add new custom EMI
              pkg.emi_options.push({
                id: 'custom',
                months: customEmi.months,
                monthly_amount: customEmi.monthly_amount,
                total_amount: customEmi.total_amount,
                processing_fee: 0,
                label: 'Custom',
                is_custom: true,
                selected: true
              });

              // Mark all other EMIs as not selected
              pkg.emi_options.forEach(emi => {
                if (emi.id !== 'custom') emi.selected = false;
              });
            }

            // Update package cards
            renderPackageCards();

            // Show success message
            notificationManager.show(`Custom ${customEmi.months}-month EMI plan selected!`, 'success');

            // Generate package card preview
            generateAndShowCardPreview(packageId);
          }

          // Function to re-render all package cards
          function renderPackageCards() {
            const packageGrid = document.getElementById('packageGrid');
            if (!packageGrid) return;

            // Get current packages from global variable
            const packages = currentPackages || [];

            if (packages.length === 0) {
              packageGrid.innerHTML = `
                <div class="no-packages">
                  <h3>No packages found</h3>
                  <p>Try searching for a different destination or travel date.</p>
                </div>
              `;
              return;
            }

            packageGrid.innerHTML = packages.map(pkg => createPackageCard(pkg)).join('');
          }

          // Select EMI plan and show package card preview
          function selectEMIPlan(packageId, emiPlanId) {
            console.log('Selected EMI plan:', { packageId, emiPlanId });

            // Store the selected EMI plan
            selectedEMIPlans[packageId] = emiPlanId;

            // Update all package cards to reflect the new EMI selection
            updatePackageCardsWithEMI(packageId, emiPlanId);

            // Show visual feedback
            notificationManager.show('EMI plan selected! Generating package card preview...', 'success');

            // Update EMI plan selection UI
            updateEMIPlanSelection(packageId, emiPlanId);

            // Show package card preview instead of quote request form
            generateAndShowCardPreview(packageId);
          }

          // Update EMI plan selection UI to show which plan is selected
          function updateEMIPlanSelection(packageId, selectedEmiId) {
            // Find all EMI plan buttons for this package
            const emiPlans = document.querySelectorAll(`[onclick*="selectEMIPlan('${packageId}'"]`);

            emiPlans.forEach(button => {
              const parentPlan = button.closest('.emi-plan');
              if (parentPlan) {
                // Remove selected class from all plans
                parentPlan.classList.remove('selected-plan');

                // Add selected class to the chosen plan
                if (button.onclick.toString().includes(selectedEmiId)) {
                  parentPlan.classList.add('selected-plan');
                }
              }
            });
          }

          // Update package cards with selected EMI plan
          function updatePackageCardsWithEMI(packageId, emiPlanId) {
            // Find the package data
            const pkg = currentPackages.find(p => p.id === packageId);
            if (!pkg || !pkg.emi_options) return;

            // Find the selected EMI option
            const selectedEMI = pkg.emi_options.find(emi => emi.id === emiPlanId);
            if (!selectedEMI) return;

            // Update the package data to mark the selected EMI
            pkg.emi_options.forEach(emi => {
              emi.selected = (emi.id === emiPlanId);
            });

            // Re-render package cards to show updated prices
            renderPackageCards();

            // Update any open package modals
            const modal = document.getElementById('packageModal');
            if (modal && modal.style.display !== 'none') {
              const currentPackageId = modal.getAttribute('data-package-id');
              if (currentPackageId === packageId) {
                openPackageModal(packageId); // Refresh the modal
              }
            }
          }

          function closePackageModal() {
            document.getElementById('packageModal').style.display = 'none';
          }

          function switchTab(tabName) {
            // Remove active class from all tabs and panes
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked tab and corresponding pane
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
          }

          // Close modals when clicking outside
          window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal-overlay')) {
              event.target.style.display = 'none';
            }
          });

          // Demo function to show results
          function showDemoResults() {
            document.getElementById('resultsTitle').innerHTML = `
              Showing packages for <span class="family-type-highlight">Stellar Duo (2 Adults)</span>
              to <span class="destination-highlight">Kashmir</span> in <span class="date-highlight">December 2024</span>
            `;

            const resultsSection = document.getElementById('resultsSection');
            resultsSection.style.display = 'block';
            resultsSection.classList.add('show');
            resultsSection.scrollIntoView({ behavior: 'smooth' });
          }

          // Initialize destination autocomplete
          function initializeDestinationAutocomplete(destinations) {
            const destinationInput = document.getElementById('destination');
            const suggestionsContainer = document.getElementById('destination-suggestions');
            let currentHighlight = -1;

            if (!destinationInput || !suggestionsContainer || !destinations) return;

            destinationInput.addEventListener('input', function() {
              const query = this.value.trim().toLowerCase();

              if (query.length < 2) {
                hideSuggestions();
                return;
              }

              // Filter destinations based on query
              const filteredDestinations = destinations.filter(dest =>
                dest.destination.toLowerCase().includes(query) ||
                dest.category.toLowerCase().includes(query)
              ).slice(0, 8); // Limit to 8 suggestions

              showSuggestions(filteredDestinations);
            });

            destinationInput.addEventListener('keydown', function(e) {
              const suggestions = suggestionsContainer.querySelectorAll('.suggestion-item');

              if (e.key === 'ArrowDown') {
                e.preventDefault();
                currentHighlight = Math.min(currentHighlight + 1, suggestions.length - 1);
                updateHighlight(suggestions);
              } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                currentHighlight = Math.max(currentHighlight - 1, -1);
                updateHighlight(suggestions);
              } else if (e.key === 'Enter') {
                e.preventDefault();
                if (currentHighlight >= 0 && suggestions[currentHighlight]) {
                  selectSuggestion(suggestions[currentHighlight]);
                }
              } else if (e.key === 'Escape') {
                hideSuggestions();
              }
            });

            // Hide suggestions when clicking outside
            document.addEventListener('click', function(e) {
              if (!destinationInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                hideSuggestions();
              }
            });

            function showSuggestions(destinations) {
              if (destinations.length === 0) {
                hideSuggestions();
                return;
              }

              suggestionsContainer.innerHTML = destinations.map(dest => `
                <div class="suggestion-item" data-destination="${dest.destination}">
                  <div>
                    <div class="suggestion-name">${dest.destination}</div>
                    <div class="suggestion-category">${dest.category}</div>
                  </div>
                  ${dest.packages_available ? `<div class="suggestion-packages">${dest.packages_available} packages</div>` : ''}
                </div>
              `).join('');

              // Add click handlers
              suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', () => selectSuggestion(item));
              });

              suggestionsContainer.classList.add('show');
              currentHighlight = -1;
            }

            function hideSuggestions() {
              suggestionsContainer.classList.remove('show');
              currentHighlight = -1;
            }

            function updateHighlight(suggestions) {
              suggestions.forEach((item, index) => {
                item.classList.toggle('highlighted', index === currentHighlight);
              });
            }

            function selectSuggestion(item) {
              const destination = item.dataset.destination;
              destinationInput.value = destination;
              hideSuggestions();
            }
          }

          // Package Card Generation Functions
          let currentPackageId = null;
          let packageCardGenerator = null;

          // Initialize package card generator
          function initPackageCardGenerator() {
            if (!packageCardGenerator) {
              packageCardGenerator = new PackageCardGenerator();
            }
          }

          // Generate and show package card preview (for EMI selection)
          async function generateAndShowCardPreview(packageId) {
            try {
              // Find the package data
              const pkg = currentPackages.find(p => p.id === packageId);
              if (!pkg) {
                notificationManager.show('Package not found', 'error');
                return;
              }

              // Show loading notification
              notificationManager.show('Generating package card preview...', 'info');

              // Get selected EMI option (check for currently selected EMI)
              const selectedEmiOption = pkg.emi_options && pkg.emi_options.length > 0 ?
                pkg.emi_options.find(emi => emi.selected) || pkg.emi_options[0] : null;

              // Prepare package data for card generation
              const nights = pkg.nights || pkg.duration_days || 5;
              const days = (pkg.duration_days || pkg.nights || 5) + 1;
              const cardData = {
                destination: pkg.destination || pkg.package_name || 'Destination',
                duration: `${nights}N/${days}D`,
                image: pkg.images && pkg.images[0] ? pkg.images[0] : './img/rectangle-14.png',
                travelMode: pkg.transport_mode || 'Train',
                nights: nights,
                mealPlan: pkg.hotels_list && pkg.hotels_list[0] ?
                  pkg.hotels_list[0].meal_plan : 'Breakfast + Dinner',
                family: {
                  name: extractFamilyTypeName(pkg.family_type) || 'FAMILY NEST',
                  composition: formatFamilyComposition(travelers)
                },
                emi: {
                  months: selectedEmiOption ? selectedEmiOption.months : 10
                },
                price: selectedEmiOption ? selectedEmiOption.monthly_amount : Math.round((pkg.total_price || 50000) / 10)
              };

              // Show preview modal with HTML card (no sharing functionality)
              showCardPreviewOnly(null, cardData);

            } catch (error) {
              console.error('Error generating package card:', error);
              notificationManager.show('Error generating package card', 'error');
            }
          }

          // Show card preview modal with integrated arun.html design (for EMI selection - with download/share)
          function showCardPreviewOnly(canvas, cardData) {
            // Find the package data for more details
            const pkg = currentPackages.find(p => p.id === currentPackageId);

            const previewHTML = `
              <div class="card-preview-modal">
                <div class="card-preview-content">
                  <div class="card-preview-header">
                    <h3>Package Card Preview - ${selectedEmiOption && selectedEmiOption.is_custom ? 'Custom ' : ''}${cardData.emi.months} Month EMI Plan</h3>
                    <button class="modal-close" onclick="closeCardPreview()">&times;</button>
                  </div>
                  <div class="card-preview-body">
                    <div class="card-preview-image">
                      <div class="travel-card" id="packagePreviewCard">
                        ${generatePackageCardHTML(cardData, pkg)}
                      </div>
                    </div>
                    <div class="card-preview-actions">
                      <button class="preview-pdf-btn" onclick="printPackagePDF('preview')" style="background: var(--primary); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; margin-right: 0.5rem;">
                        <i class="fas fa-eye"></i> Preview PDF
                      </button>
                      <button class="download-card-btn" onclick="downloadPackageCard()" style="background: var(--primary); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; margin-right: 0.5rem;">
                        <i class="fas fa-download"></i> Download Card
                      </button>
                      <button class="share-btn" onclick="sharePackageCard()" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-share-alt"></i> Share Card
                      </button>
                      <button class="close-preview-btn" onclick="closeCardPreview()" style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-times"></i> Close
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', previewHTML);

            // Store data for reference
            window.currentCardData = cardData;
            window.currentPackageData = pkg;
          }

          // Show card preview modal with integrated arun.html design (legacy function - kept for compatibility)
          function showCardPreview(canvas, cardData) {
            // Find the package data for more details
            const pkg = currentPackages.find(p => p.id === currentPackageId);

            const previewHTML = `
              <div class="card-preview-modal">
                <div class="card-preview-content">
                  <div class="card-preview-header">
                    <h3>Package Card Preview</h3>
                    <button class="modal-close" onclick="closeCardPreview()">&times;</button>
                  </div>
                  <div class="card-preview-body">
                    <div class="card-preview-image">
                      <div class="travel-card" id="packagePreviewCard">
                        ${generatePackageCardHTML(cardData, pkg)}
                      </div>
                    </div>
                    <div class="card-preview-actions">
                      <button class="download-card-btn" onclick="downloadPackageCard()" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; margin-right: 0.5rem;">
                        <i class="fas fa-download"></i> Download Card
                      </button>
                      <button class="share-btn" onclick="sharePackageCard()" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-share-alt"></i> Share Card
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', previewHTML);

            // Store data for download/share
            window.currentCardData = cardData;
            window.currentPackageData = pkg;
          }

          // Generate package card HTML using arun.html design
          function generatePackageCardHTML(cardData, pkg) {
            // Get the best EMI option
            const selectedEmiOption = pkg && pkg.emi_options && pkg.emi_options.length > 0 ?
              pkg.emi_options.find(emi => emi.selected) || pkg.emi_options[0] : null;

            // Get transport mode
            const transportMode = pkg && pkg.transport_mode ? pkg.transport_mode : 'Train';
            const transportIcon = getTransportIcon(transportMode);

            // Get inclusions
            const sanitizeList = (list, fallback) => {
              const cleaned = (Array.isArray(list) ? list : [])
                .map(item => (item || '').toString().trim())
                .filter(Boolean);
              return (cleaned.length ? cleaned : fallback).slice(0, 5);
            };
            const inclusions = sanitizeList(pkg.inclusions, ['Accommodation', 'Transfers', 'Sight-seeing']);
            const exclusions = sanitizeList(pkg.exclusions, ['Personal expenses', 'Travel insurance', 'Items not mentioned in inclusions']);

            // Get family type info
            const familyType = detectFamilyType();

            // Get image - use online fallback to avoid CORS issues
            let packageImage = pkg && pkg.images && pkg.images[0] ? pkg.images[0] : null;

            // If no package image or it's a local file, use destination-specific online images
            if (!packageImage || packageImage.includes('file://') || packageImage.includes('img/rectangle')) {
              const destination = cardData.destination.toLowerCase();
              const destinationImages = {
              'andaman': 'https://images.unsplash.com/photo-1640718835374-6116a99c6e6c?w=800&h=400&fit=crop',
              'coorg': 'https://images.unsplash.com/photo-1602216056096-3b40cc0c9944?w=800&h=400&fit=crop',
              'goa': 'https://images.unsplash.com/photo-1512343879784-a960bf40e7f2?w=800&h=400&fit=crop',
              'manali': 'https://images.unsplash.com/photo-1626621341517-bbf3d9990a23?w=800&h=400&fit=crop',
              'munnar': 'https://images.unsplash.com/photo-1602216056096-3b40cc0c9944?w=800&h=400&fit=crop',
              'varkala': 'https://images.unsplash.com/photo-1697193375091-f9bfe3d7ed7b?w=800&h=400&fit=crop',
              'default': 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600'
              };

              // Find matching destination image
              packageImage = destinationImages[destination] || destinationImages['default'];

              // Check for partial matches
              if (packageImage === destinationImages['default']) {
                for (const [key, value] of Object.entries(destinationImages)) {
                  if (destination.includes(key) || key.includes(destination)) {
                    packageImage = value;
                    break;
                  }
                }
              }
            }

            return `
              <div class="card-header">
                <img src="${packageImage}"
                     alt="${cardData.destination} package"
                     class="card-image"
                     crossorigin="anonymous"
                     onload="this.style.opacity='1'"
                     onerror="this.src='https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600'"
                     style="opacity: 0; transition: opacity 0.3s ease;">
                <div class="image-overlay"></div>

                <div class="plan-badge">
                  <svg class="crown-icon" fill="currentColor" viewBox="0 0 24 24" style="width: 16px; height: 16px; margin-right: 6px;">
                    <path d="M5 16L3 6l5.5 4L12 4l3.5 6L21 6l-2 10H5zm2.7-2h8.6l.9-4.4L14 12l-2-3.4L10 12l-3.2-2.4L7.7 14z"/>
                  </svg>
                  Gold Plan
                </div>

                <div class="duration-badge">
                  <svg class="duration-icon" fill="currentColor" viewBox="0 0 24 24" style="width: 16px; height: 16px; margin-right: 6px;">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  ${cardData.duration}
                </div>

                <!-- Location overlay on image -->
                <div class="location-overlay">
                  <h2 class="destination-name-overlay">${generatePackageTitle(cardData, pkg)}</h2>
                </div>
              </div>

                <div class="card-content">
                <!-- Package Name Section -->
                <div class="package-name-section">
                <h3 class="package-name-title">${pkg && pkg.quote_name ? pkg.quote_name : pkg && pkg.package_name ? pkg.package_name : pkg && pkg.title ? pkg.title : cardData.destination + ' Travel Package'}</h3>
                  <p class="package-name-subtitle">Curated for your perfect getaway</p>
                </div>

                <div class="destination-info">
                  <h2 class="destination-name">${cardData.destination}</h2>
                  <p class="location">${pkg && pkg.state ? pkg.state + ', India' : 'India'}</p>
                </div>

                <div class="family-type-main-section">
                  <div class="family-type-info">
                    <div class="family-type-left">
                      <div class="family-icon-wrapper-main">
                        <svg class="family-icon-main" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.5 7.5h-5A1.5 1.5 0 0 0 12.04 8.37L9.5 16H12v6h8zM12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5zM5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2zm1.5 1h-3C2.67 7 2 7.67 2 8.5v7c0 .83.67 1.5 1.5 1.5H4v5h4v-5h.5c.83 0 1.5-.67 1.5-1.5v-7C10 7.67 9.33 7 8.5 7z"/>
                        </svg>
                      </div>
                      <div>
                        <span class="family-type-text">Family Type:</span>
                       <p class="family-type-desc">${familyType.family_type} </p>
                      </div>
                    </div>
                    <div class="family-type-right">
                      <div class="family-tripxplo-text-logo">
                        <span class="trip-text">TRIP</span><span class="xplo-text">XPLO</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="amenities-section">
                  <h3 class="amenities-title">What's Included</h3>
                  <div class="amenities-list-vertical">
                    ${inclusions.slice(0, 4).map((inclusion, index) => {
                      // Convert meal plan codes to descriptions and handle hotel names
                      let displayInclusion = inclusion;

                      // Check if it's a standalone meal plan code
                      if (inclusion && typeof inclusion === 'string') {
                        const trimmedInclusion = inclusion.trim().toUpperCase();
                        if (trimmedInclusion === 'MAP' || trimmedInclusion === 'CP' || trimmedInclusion === 'EP' || trimmedInclusion === 'AP') {
                          displayInclusion = getMealPlanDescription(inclusion);
                        } else {
                          // Check if it's a hotel name with meal plan code at the end
                          const mealPlanRegex = /\s*\((EP|CP|MAP|AP)\)\s*$/i;
                          const match = inclusion.match(mealPlanRegex);
                          if (match) {
                            const mealPlanCode = match[1];
                            const hotelNamePart = inclusion.replace(mealPlanRegex, '').trim();
                            const mealPlanDescription = getMealPlanDescription(mealPlanCode);
                            displayInclusion = `${hotelNamePart} (${mealPlanDescription})`;
                          }
                        }
                      }

                      return `
                      <div class="amenity-item-vertical">
                        <div class="amenity-icon-wrapper-vertical ${getInclusionIconClass(displayInclusion, index)}">
                          ${getInclusionIconSVG(displayInclusion, index)}
                        </div>
                        <div class="amenity-text-vertical">
                          <span class="amenity-name-vertical">${displayInclusion}</span>
                        </div>
                      </div>
                      `;
                    }).join('')}
                  </div>
                </div>

                <div class="price-section">
                  <div class="payment-plan">
                    <span class="payment-badge">Pay in ${selectedEmiOption ? selectedEmiOption.months : cardData.emi.months} months</span>
                  </div>
                  <button class="price-button" onclick="selectPackage('${pkg ? pkg.id : 'unknown'}')">
                    <div>
                      <div class="price-main">₹${cardData.price.toLocaleString()}</div>
                      <div class="price-desc">per month</div>
                    </div>
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            `;
          }

          // Close card preview
          function closeCardPreview() {
            const modal = document.querySelector('.card-preview-modal');
            if (modal) {
              modal.remove();
            }
          }

          // Helper function to get transport icon
          function getTransportIcon(transportMode) {
            const icons = {
              'Train': '<svg class="transport-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17a2 2 0 11-4 0 2 2 0 014 0zm4 0a2 2 0 11-4 0 2 2 0 014 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l2.414 2.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"/></svg>',
              'Flight': '<svg class="transport-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/></svg>',
              'Bus': '<svg class="transport-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 17l4 4 4-4m-4-5v9"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.88 18.09A5 5 0 0018 9h-1.26A8 8 0 103 16.29"/></svg>'
            };
            return icons[transportMode] || icons['Train'];
          }

          // Helper function to get inclusion icon SVG - Comprehensive icons
          function getInclusionIconSVG(inclusion, index) {
            const icons = {
              // 🎯 Sightseeing - Location pin icon
              'All sightseeing as per itinerary': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>',
              'Sightseeing': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>',
              // 🏨 Hotels/Hotel - Hotel building icon
              'Hotels': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H3V6H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z"/></svg>',
              'Hotel': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H3V6H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z"/></svg>',

              // ✈️ Flights/Flight - Airplane icon
              'Airport transfers': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/></svg>',
              'Flight': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/></svg>',

              // 🍽️ Meals/Breakfast/Lunch/Dinner - Utensils/food icons
              'Meals': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M11 9H9V2H7v7H5V2H3v7c0 2.12 1.66 3.84 3.75 3.97V22h2.5v-9.03C11.34 12.84 13 11.12 13 9V2h-2v7zm5-3v8h2.5v8H21V2c-2.76 0-5 2.24-5 4z"/></svg>',
              'Meal': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M11 9H9V2H7v7H5V2H3v7c0 2.12 1.66 3.84 3.75 3.97V22h2.5v-9.03C11.34 12.84 13 11.12 13 9V2h-2v7zm5-3v8h2.5v8H21V2c-2.76 0-5 2.24-5 4z"/></svg>',
              'Breakfast': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M20 3H4v10c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4v-3h2c1.11 0 2-.9 2-2V5c0-1.11-.89-2-2-2zm0 5h-2V5h2v3z"/></svg>',
              'Lunch': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M11 9H9V2H7v7H5V2H3v7c0 2.12 1.66 3.84 3.75 3.97V22h2.5v-9.03C11.34 12.84 13 11.12 13 9V2h-2v7zm5-3v8h2.5v8H21V2c-2.76 0-5 2.24-5 4z"/></svg>',
              'Dinner': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M8.1 13.34l2.83-2.83L3.91 3.5c-1.56 1.56-1.56 4.09 0 5.66l4.19 4.18zm6.78-1.81c1.53.71 3.68.21 5.27-1.38 1.91-1.91 2.28-4.65.81-6.12-1.46-1.46-4.20-1.10-6.12.81-1.59 1.59-2.09 3.74-1.38 5.27L3.7 19.87l1.41 1.41L12 14.41l6.88 6.88 1.41-1.41L13.41 13l1.47-1.47z"/></svg>',

              // Meal Plan Descriptions
              'Room Only': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H3V6H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z"/></svg>',
              'Breakfast Included': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M20 3H4v10c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4v-3h2c1.11 0 2-.9 2-2V5c0-1.11-.89-2-2-2zm0 5h-2V5h2v3z"/></svg>',
              'Breakfast and Dinner Included': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M11 9H9V2H7v7H5V2H3v7c0 2.12 1.66 3.84 3.75 3.97V22h2.5v-9.03C11.34 12.84 13 11.12 13 9V2h-2v7zm5-3v8h2.5v8H21V2c-2.76 0-5 2.24-5 4z"/></svg>',
              'All Meals Included': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M11 9H9V2H7v7H5V2H3v7c0 2.12 1.66 3.84 3.75 3.97V22h2.5v-9.03C11.34 12.84 13 11.12 13 9V2h-2v7zm5-3v8h2.5v8H21V2c-2.76 0-5 2.24-5 4z"/></svg>',

              // 🚗 Local Cab/Cab/Transport/Transfers - Car icon
              'Local Cab': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>',
              'Cab': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>',
              'Transport': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>',
              'Transfers': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>',

              // 🏃 Activities/Adventure - Activity icon
              'Activities': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M13.49 5.48c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm-3.6 13.9l1-4.4 2.1 2v6h2v-7.5l-2.1-2 .6-3c1.3 1.5 3.3 2.5 5.5 2.5v-2c-1.9 0-3.5-1-4.3-2.4l-1-1.6c-.4-.6-1-1-1.7-1-.3 0-.5.1-.8.1l-5.2 2.2v4.7h2v-3.4l1.8-.7-1.6 8.1-4.9-1-.4 2 7 1.4z"/></svg>',
              'Activity': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M13.49 5.48c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm-3.6 13.9l1-4.4 2.1 2v6h2v-7.5l-2.1-2 .6-3c1.3 1.5 3.3 2.5 5.5 2.5v-2c-1.9 0-3.5-1-4.3-2.4l-1-1.6c-.4-.6-1-1-1.7-1-.3 0-.5.1-.8.1l-5.2 2.2v4.7h2v-3.4l1.8-.7-1.6 8.1-4.9-1-.4 2 7 1.4z"/></svg>',
              'Adventure': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M14 6l-3.75 5 2.85 3.8-1.6 1.2C9.81 13.75 7 10 7 10l-6 8h22L14 6z"/></svg>',

              // 🏠 Accommodation - Building icon
              'Accommodation': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H3V6H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z"/></svg>',

              // 🏖️ Beach Activities - Beach umbrella icon
              'Beach Activities': '<svg class="amenity-icon" fill="currentColor" viewBox="0 0 24 24"><path d="M13 4.07V1h-2v3.07c-4.39.49-7.5 4.21-7.5 8.93 0 4.97 4.02 9 9 9s9-4.03 9-9c0-4.72-3.11-8.44-7.5-8.93zM12 20c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"/></svg>'
            };
            return icons[inclusion] || icons['Sightseeing'];
          }

          // Helper function to get inclusion icon class with smart detection
          function getInclusionIconClass(inclusion, index) {
            // Smart detection for hotel names with meal plan codes
            if (inclusion && typeof inclusion === 'string') {
              const lowerInclusion = inclusion.toLowerCase();

              // Check for hotel-related keywords
              if (lowerInclusion.includes('hotel') || lowerInclusion.includes('resort') ||
                  lowerInclusion.includes('accommodation') || lowerInclusion.includes('stay') ||
                  lowerInclusion.includes('lodge') || lowerInclusion.includes('villa') ||
                  lowerInclusion.includes('guest house') || lowerInclusion.includes('homestay') ||
                  /\d+n\s*-/.test(lowerInclusion)) { // Pattern like "3N - Hotel Name"
                return 'hotel-icon-wrapper';
              }

              // Check for transport-related keywords
              if (lowerInclusion.includes('cab') || lowerInclusion.includes('taxi') ||
                  lowerInclusion.includes('transfer') || lowerInclusion.includes('transport') ||
                  lowerInclusion.includes('pickup') || lowerInclusion.includes('drop')) {
                return 'cab-icon-wrapper';
              }

              // Check for flight-related keywords
              if (lowerInclusion.includes('flight') || lowerInclusion.includes('airfare') ||
                  lowerInclusion.includes('air ticket') || lowerInclusion.includes('plane')) {
                return 'flight-icon-wrapper';
              }

              // Check for meal-related keywords
              if (lowerInclusion.includes('meal') || lowerInclusion.includes('breakfast') ||
                  lowerInclusion.includes('lunch') || lowerInclusion.includes('dinner') ||
                  lowerInclusion.includes('food') || lowerInclusion.includes('dining')) {
                return 'meal-icon-wrapper';
              }

              // Check for sightseeing-related keywords
              if (lowerInclusion.includes('sightseeing') || lowerInclusion.includes('tour') ||
                  lowerInclusion.includes('visit') || lowerInclusion.includes('attraction') ||
                  lowerInclusion.includes('excursion')) {
                return 'sightseeing-icon-wrapper';
              }

              // Check for activity-related keywords
              if (lowerInclusion.includes('activity') || lowerInclusion.includes('adventure') ||
                  lowerInclusion.includes('sport') || lowerInclusion.includes('experience')) {
                return 'activity-icon-wrapper';
              }
            }

            // Fallback to exact matches
            const classes = {
              'All sightseeing as per itinerary': 'sightseeing-icon-wrapper',
              'Local Cab': 'cab-icon-wrapper',
              'Cab': 'cab-icon-wrapper',
              'Meals': 'meal-icon-wrapper',
              'Meal': 'meal-icon-wrapper',
              'Breakfast': 'meal-icon-wrapper',
              'Lunch': 'meal-icon-wrapper',
              'Dinner': 'meal-icon-wrapper',
              'Room Only': 'hotel-icon-wrapper',
              'Breakfast Included': 'meal-icon-wrapper',
              'Breakfast and Dinner Included': 'meal-icon-wrapper',
              'All Meals Included': 'meal-icon-wrapper',
              'Hotels': 'hotel-icon-wrapper',
              'Hotel': 'hotel-icon-wrapper',
              'Airport transfers': 'flight-icon-wrapper',
              'Flight': 'flight-icon-wrapper',
              'Activities': 'activity-icon-wrapper',
              'Activity': 'activity-icon-wrapper',
              'Transport': 'transport-icon-wrapper',
              'Transfers': 'transport-icon-wrapper',
              'Accommodation': 'accommodation-icon-wrapper',
              'Adventure': 'activity-icon-wrapper',
              'Beach Activities': 'activity-icon-wrapper'
            };
            return classes[inclusion] || 'hotel-icon-wrapper'; // Default to hotel icon for unknown items
          }

          // Generate clean location name (just the destination like "Goa")
          function generatePackageTitle(cardData, pkg) {
            let destination = cardData.destination;

            // Clean up the destination name - remove complex descriptions
            if (destination.includes(':')) {
              destination = destination.split(':')[0].trim();
            }

            // Remove existing duration patterns and extra text
            destination = destination.replace(/^\d+[ND]\s*/gi, '').trim();
            destination = destination.replace(/Popular$/gi, '').trim();

            // Remove complex family composition text
            destination = destination.replace(/\s*-\s*\d+\s*Adult.*$/gi, '').trim();

            // Remove common suffixes to get just the location
            const suffixesToRemove = [
              'Family Getaway', 'Package', 'Tour', 'Trip', 'Combo',
              'Special', 'Deluxe', 'Premium', 'Standard'
            ];

            suffixesToRemove.forEach(suffix => {
              const regex = new RegExp(`\\s*${suffix}\\s*$`, 'gi');
              destination = destination.replace(regex, '').trim();
            });

            // Handle multi-destination cases - take first destination only
            if (destination.includes('&')) {
              destination = destination.split('&')[0].trim();
            }
            if (destination.includes(',')) {
              destination = destination.split(',')[0].trim();
            }

            // Remove any remaining numbers or special characters at the end
            destination = destination.replace(/[\d\-\+\(\)]+.*$/, '').trim();

            // Remove any remaining "and" or "&" at the end
            destination = destination.replace(/\s+(and|&)\s*$/gi, '').trim();

            return destination || cardData.destination; // Fallback to original if cleaning fails
          }

          // Helper function to get meal plan description
          const getMealPlanDescription = (code) => {
            if (!code) return 'N/A';

            switch (code.toUpperCase()) {
              case 'EP': return 'Room Only';
              case 'CP': return 'Breakfast Included';
              case 'MAP': return 'Breakfast and Dinner Included';
              case 'AP': return 'All Meals Included';
              default: return code;
            }
          };

          // Helper function to get inclusion description
          function getInclusionDescription(inclusion) {
            const descriptions = {
              'Sightseeing': 'Guided tours',
              'Local Cab': 'Pick & drop',
              'Meals': 'As per plan',
              'Hotels': 'Accommodation',
              'Flights': 'Round trip',
              'Activities': 'Adventure sports'
            };
            return descriptions[inclusion] || 'Included';
          }

          // Handle package selection from price button
          function selectPackage(packageId) {
            // Close the preview modal if open
            closeCardPreview();

            // Show package selection message
            notificationManager.show('Package selected! Please provide your contact details.', 'info');

            // Open contact details modal
            setTimeout(() => {
              openContactDetailsModal(packageId);
            }, 300);
          }

          // Format family composition for card without duplicates
          function formatFamilyComposition(travelers) {
            let parts = [];

            if (travelers.adults > 0) {
              parts.push(`${travelers.adults} ADULT${travelers.adults > 1 ? 'S' : ''}`);
            }

            if (travelers.child > 0) {
              parts.push(`${travelers.child} CHILD${travelers.child > 1 ? 'REN' : ''} (2-5 YRS)`);
            }

            if (travelers.children > 0) {
              parts.push(`${travelers.children} CHILD${travelers.children > 1 ? 'REN' : ''} (6-11 YRS)`);
            }

            if (travelers.teenagers > 0) {
              parts.push(`${travelers.teenagers} TEENAGER${travelers.teenagers > 1 ? 'S' : ''} (ABOVE 11 YRS)`);
            }

            if (travelers.infants > 0) {
              parts.push(`${travelers.infants} INFANT${travelers.infants > 1 ? 'S' : ''} (BELOW 2 YRS)`);
            }

            return parts.join(' + ');
          }

          // Family Type Info Modal Functions
          function openFamilyTypeInfoModal() {
            const modal = document.getElementById('familyTypeInfoModal');
            const modalContent = document.getElementById('familyTypeModalContent');

            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Load family type data when modal opens
            loadFamilyTypeData();

            // Initialize enhanced scroll functionality
            initializeModalScroll(modalContent);

            // Add click outside to close functionality
            modal.addEventListener('click', function(e) {
              if (e.target === modal) {
                closeFamilyTypeInfoModal();
              }
            });

            // Add keyboard support (ESC key)
            document.addEventListener('keydown', handleFamilyTypeModalKeydown);
          }

          // Initialize enhanced scroll functionality for modal
          function initializeModalScroll(modalContent) {
            const scrollIndicator = document.getElementById('scrollIndicator');
            const scrollDots = scrollIndicator.querySelectorAll('.scroll-dot');

            // Update scroll indicators and shadows
            function updateScrollState() {
              const scrollTop = modalContent.scrollTop;
              const scrollHeight = modalContent.scrollHeight;
              const clientHeight = modalContent.clientHeight;
              const maxScroll = scrollHeight - clientHeight;

              // Update scroll shadows
              if (scrollTop > 10) {
                modalContent.classList.add('has-scroll-top');
              } else {
                modalContent.classList.remove('has-scroll-top');
              }

              if (scrollTop < maxScroll - 10) {
                modalContent.classList.add('has-scroll-bottom');
              } else {
                modalContent.classList.remove('has-scroll-bottom');
              }

              // Update scroll dots
              if (maxScroll > 0) {
                const scrollPercentage = scrollTop / maxScroll;
                const activeIndex = Math.round(scrollPercentage * (scrollDots.length - 1));

                scrollDots.forEach((dot, index) => {
                  dot.classList.toggle('active', index === activeIndex);
                });

                // Show indicator if content is scrollable
                scrollIndicator.style.display = 'flex';
              } else {
                scrollIndicator.style.display = 'none';
              }
            }

            // Add scroll event listener
            modalContent.addEventListener('scroll', updateScrollState);

            // Initial update after content loads
            setTimeout(updateScrollState, 100);

            // Update when content changes
            const observer = new MutationObserver(updateScrollState);
            observer.observe(modalContent, { childList: true, subtree: true });

            // Store observer for cleanup
            modalContent._scrollObserver = observer;
          }

          function handleFamilyTypeModalKeydown(e) {
            const modalContent = document.getElementById('familyTypeModalContent');

            if (e.key === 'Escape') {
              closeFamilyTypeInfoModal();
            } else if (e.key === 'ArrowUp' && e.ctrlKey) {
              // Ctrl + Up Arrow: Scroll to top
              e.preventDefault();
              modalContent.scrollTo({ top: 0, behavior: 'smooth' });
            } else if (e.key === 'ArrowDown' && e.ctrlKey) {
              // Ctrl + Down Arrow: Scroll to bottom
              e.preventDefault();
              modalContent.scrollTo({ top: modalContent.scrollHeight, behavior: 'smooth' });
            } else if (e.key === 'PageUp') {
              // Page Up: Scroll up by half viewport
              e.preventDefault();
              const scrollAmount = modalContent.clientHeight * 0.5;
              modalContent.scrollBy({ top: -scrollAmount, behavior: 'smooth' });
            } else if (e.key === 'PageDown') {
              // Page Down: Scroll down by half viewport
              e.preventDefault();
              const scrollAmount = modalContent.clientHeight * 0.5;
              modalContent.scrollBy({ top: scrollAmount, behavior: 'smooth' });
            }
          }

          function closeFamilyTypeInfoModal() {
            const modal = document.getElementById('familyTypeInfoModal');
            const modalContent = document.getElementById('familyTypeModalContent');

            modal.style.display = 'none';
            document.body.style.overflow = 'auto';

            // Clean up scroll observer
            if (modalContent._scrollObserver) {
              modalContent._scrollObserver.disconnect();
              delete modalContent._scrollObserver;
            }

            // Remove keyboard event listener
            document.removeEventListener('keydown', handleFamilyTypeModalKeydown);
          }

          // Load family type data from database
          async function loadFamilyTypeData() {
            const loadingElement = document.getElementById('familyTypeLoading');
            const tableContainer = document.getElementById('familyTypeTableContainer');
            const errorElement = document.getElementById('familyTypeError');

            // Show loading state
            loadingElement.style.display = 'flex';
            tableContainer.style.display = 'none';
            errorElement.style.display = 'none';

            try {
              console.log('🔄 Loading family type pricing data from database...');

              // Fetch data from family_type_prices table
              const response = await databaseService.getFamilyTypePricesData();

              if (response.success && response.data && response.data.length > 0) {
                console.log('✅ Loaded family type prices:', response.data.length);
                displayFamilyTypeData(response.data);

                // Hide loading and show table
                loadingElement.style.display = 'none';
                tableContainer.style.display = 'block';

                // Smooth scroll to top when content loads
                const modalContent = document.getElementById('familyTypeModalContent');
                if (modalContent) {
                  setTimeout(() => {
                    modalContent.scrollTo({ top: 0, behavior: 'smooth' });
                  }, 100);
                }
              } else {
                throw new Error(response.message || 'No family type data found');
              }
            } catch (error) {
              console.error('❌ Error loading family type data:', error);

              // Hide loading and show error
              loadingElement.style.display = 'none';
              errorElement.style.display = 'flex';
            }
          }

          // Display family type data in table
          function displayFamilyTypeData(data) {
            const tableBody = document.getElementById('familyTypeTableBody');

            // Group data by family type to remove duplicates
            const familyTypeGroups = {};

            data.forEach(item => {
              const key = item.family_type_name || 'Unknown';
              if (!familyTypeGroups[key]) {
                familyTypeGroups[key] = {
                  family_type_name: item.family_type_name,
                  no_of_adults: item.no_of_adults,
                  no_of_children: item.no_of_children,
                  no_of_child: item.no_of_child,
                  no_of_infants: item.no_of_infants,
                  family_count: item.family_count
                };
              }
            });

            // Generate table rows without pricing
            const rows = Object.values(familyTypeGroups).map(group => {
              return `
                <tr>
                  <td class="family-type-name">${group.family_type_name || 'N/A'}</td>
                  <td class="count-cell">${group.no_of_adults || 0}</td>
                  <td class="count-cell">${group.no_of_children || 0}</td>
                  <td class="count-cell">${group.no_of_child || 0}</td>
                  <td class="count-cell">${group.no_of_infants || 0}</td>
                  <td class="count-cell">${group.family_count || 0}</td>
                </tr>
              `;
            });

            tableBody.innerHTML = rows.join('');
          }

          // PDF Generation Function for Family Types
          async function printFamilyTypePDF() {
            try {
              console.log('🖨️ Generating Family Types PDF...');

              // Show loading state on print button
              const printBtn = document.querySelector('.print-btn');
              const originalHTML = printBtn.innerHTML;
              printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
              printBtn.disabled = true;

              // Get family type data from the table
              const tableBody = document.getElementById('familyTypeTableBody');
              const rows = tableBody.querySelectorAll('tr');

              if (rows.length === 0) {
                throw new Error('No family type data available to print');
              }

              // Extract data from table
              const familyTypeData = [];
              rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 6) {
                  familyTypeData.push({
                    name: cells[0].textContent.trim(),
                    adults: cells[1].textContent.trim(),
                    children: cells[2].textContent.trim(),
                    child: cells[3].textContent.trim(),
                    infants: cells[4].textContent.trim(),
                    total: cells[5].textContent.trim()
                  });
                }
              });

              // Create PDF using jsPDF
              const { jsPDF } = window.jspdf;
              const doc = new jsPDF('portrait', 'mm', 'a4');

              // PDF Configuration
              const pageWidth = doc.internal.pageSize.getWidth();
              const pageHeight = doc.internal.pageSize.getHeight();
              const margin = 15;
              const contentWidth = pageWidth - (margin * 2);

              // Colors
              const primaryColor = [16, 185, 129]; // TripXplo green
              const darkColor = [31, 41, 55];
              const lightGray = [107, 114, 128];
              const borderColor = [229, 231, 235];

              // Simple header function - minimal design
              function addSimpleHeader(doc) {
                return margin; // Start content immediately at margin
              }

              // Simple table headers with proper spacing and line breaks
              function addSimpleTableHeaders(doc, startY) {
                const headers = [
                  { text: 'Family Type Name', lines: ['Family Type Name'] },
                  { text: 'Adults', lines: ['Adults'] },
                  { text: 'Children\n(6-11)', lines: ['Children', '(6-11)'] },
                  { text: 'Child\n(2-5)', lines: ['Child', '(2-5)'] },
                  { text: 'Infants\n(0-2)', lines: ['Infants', '(0-2)'] },
                  { text: 'Total\nCount', lines: ['Total', 'Count'] }
                ];
                const colWidths = [90, 16, 20, 18, 18, 18];
                let currentX = margin;
                const headerHeight = 16; // Increased height for two-line headers

                // Simple header background - similar to modal
                doc.setFillColor(...primaryColor);
                doc.rect(margin, startY, contentWidth, headerHeight, 'F');

                doc.setTextColor(255, 255, 255);
                doc.setFontSize(8); // Smaller font for better fit
                doc.setFont('helvetica', 'bold');

                headers.forEach((header, index) => {
                  if (header.lines.length === 1) {
                    // Single line header - center vertically
                    doc.text(header.lines[0], currentX + 2, startY + 10);
                  } else {
                    // Multi-line header
                    doc.text(header.lines[0], currentX + 2, startY + 6);
                    doc.text(header.lines[1], currentX + 2, startY + 12);
                  }
                  currentX += colWidths[index];
                });

                return startY + headerHeight;
              }

              // Helper function to wrap text within a given width
              function wrapText(doc, text, maxWidth) {
                const words = text.split(' ');
                const lines = [];
                let currentLine = '';

                words.forEach(word => {
                  const testLine = currentLine ? currentLine + ' ' + word : word;
                  const testWidth = doc.getTextWidth(testLine);

                  if (testWidth <= maxWidth) {
                    currentLine = testLine;
                  } else {
                    if (currentLine) {
                      lines.push(currentLine);
                      currentLine = word;
                    } else {
                      // Single word is too long, force it
                      lines.push(word);
                      currentLine = '';
                    }
                  }
                });

                if (currentLine) {
                  lines.push(currentLine);
                }

                return lines;
              }

              // Simple data rows with text wrapping and primary color for family names
              function addSimpleDataRows(doc, data, startY) {
                const colWidths = [90, 16, 20, 18, 18, 18]; // Match header widths
                let currentY = startY;

                doc.setFont('helvetica', 'normal');
                doc.setFontSize(8);

                data.forEach((item, index) => {
                  let currentX = margin;

                  // Calculate row height based on wrapped text
                  const familyName = item.name;
                  const maxWidth = colWidths[0] - 4; // Leave padding
                  const wrappedLines = wrapText(doc, familyName, maxWidth);
                  const rowHeight = Math.max(12, wrappedLines.length * 6 + 6); // Dynamic height based on lines

                  // Simple alternate row colors - like the modal
                  if (index % 2 === 0) {
                    doc.setFillColor(248, 250, 252);
                    doc.rect(margin, currentY, contentWidth, rowHeight, 'F');
                  }

                  // Add data with proper text handling
                  const rowData = [item.name, item.adults, item.children, item.child, item.infants, item.total];

                  rowData.forEach((data, colIndex) => {
                    if (colIndex === 0) {
                      // Family name in primary color with text wrapping
                      doc.setTextColor(...primaryColor); // Use primary color for family names
                      doc.setFont('helvetica', 'normal');

                      // Draw each line of wrapped text
                      wrappedLines.forEach((line, lineIndex) => {
                        doc.text(line, currentX + 2, currentY + 8 + (lineIndex * 6));
                      });
                    } else {
                      // Numbers in dark color, center aligned
                      doc.setTextColor(...darkColor);
                      doc.setFont('helvetica', 'normal');

                      const textWidth = doc.getTextWidth(data);
                      const centerX = currentX + (colWidths[colIndex] / 2) - (textWidth / 2);
                      const centerY = currentY + (rowHeight / 2) + 2; // Vertically center in the row
                      doc.text(data, centerX, centerY);
                    }
                    currentX += colWidths[colIndex];
                  });

                  currentY += rowHeight;
                });

                return currentY;
              }

              // No footer function needed for simple design

              // Split data for two pages
              const itemsPerPage = Math.ceil(familyTypeData.length / 2);
              const page1Data = familyTypeData.slice(0, itemsPerPage);
              const page2Data = familyTypeData.slice(itemsPerPage);

              // Generate Page 1 - Simple design
              let currentY = addSimpleHeader(doc);
              currentY = addSimpleTableHeaders(doc, currentY);
              currentY = addSimpleDataRows(doc, page1Data, currentY);

              // Generate Page 2 - Simple design
              doc.addPage();
              currentY = addSimpleHeader(doc);
              currentY = addSimpleTableHeaders(doc, currentY);
              currentY = addSimpleDataRows(doc, page2Data, currentY);

              // Generate filename with timestamp
              const timestamp = new Date().toISOString().split('T')[0];
              const filename = `TripXplo_Family_Types_${timestamp}.pdf`;

              // Save the PDF
              doc.save(filename);

              console.log('✅ Optimized PDF generated successfully:', filename);

            } catch (error) {
              console.error('❌ Error generating PDF:', error);
              alert('Error generating PDF: ' + error.message);
            } finally {
              // Reset print button
              const printBtn = document.querySelector('.print-btn');
              printBtn.innerHTML = '<i class="fas fa-print"></i>';
              printBtn.disabled = false;
            }
          }

          // Contact Details Modal Functions
          let selectedPackageId = null;
          let selectedPackageContext = null;

          function openContactDetailsModal(packageId) {
            selectedPackageId = packageId;

            // Store package context for form submission
            selectedPackageContext = {
              packageId: packageId,
              searchParams: {
                destination: document.getElementById('destination')?.value || '',
                travel_date: document.getElementById('travelMonth')?.value || '',
                specific_date: document.getElementById('specificDate')?.value || '',
                adults: travelers.adults,
                children: travelers.children,
                child: travelers.child,
                infants: travelers.infants
              },
              selectedPackage: currentPackages.find(pkg => pkg.id === packageId),
              timestamp: new Date().toISOString()
            };

            console.log('📦 Package context stored for contact form:', selectedPackageContext);

            const modal = document.getElementById('contactDetailsModal');
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Reset form
            resetContactForm();

            // Add click outside to close functionality
            modal.addEventListener('click', function(e) {
              if (e.target === modal) {
                closeContactDetailsModal();
              }
            });

            // Add keyboard support (ESC key)
            document.addEventListener('keydown', handleContactModalKeydown);
          }

          function closeContactDetailsModal() {
            const modal = document.getElementById('contactDetailsModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';

            // Remove event listeners
            document.removeEventListener('keydown', handleContactModalKeydown);

            // Reset form and clear context
            resetContactForm();
            selectedPackageId = null;
            selectedPackageContext = null;
          }

          function handleContactModalKeydown(e) {
            if (e.key === 'Escape') {
              closeContactDetailsModal();
            }
          }

          function resetContactForm() {
            const form = document.getElementById('contactDetailsForm');
            form.reset();

            // Clear all error states
            const inputs = form.querySelectorAll('.form-input');
            inputs.forEach(input => {
              input.classList.remove('error', 'success');
            });

            // Hide all error messages
            const errorMessages = form.querySelectorAll('.error-message');
            errorMessages.forEach(error => {
              error.classList.remove('show');
              error.textContent = '';
            });

            // Reset submit button
            const submitBtn = form.querySelector('.submit-btn');
            submitBtn.disabled = false;
            submitBtn.classList.remove('loading');
          }

          // Form validation functions
          function validateFirstName(value) {
            if (!value || value.trim().length < 2) {
              return 'First name must be at least 2 characters long';
            }
            if (!/^[a-zA-Z\s]+$/.test(value.trim())) {
              return 'First name should only contain letters and spaces';
            }
            return null;
          }

          function validateLastName(value) {
            if (!value || value.trim().length < 1) {
              return 'Last name must be at least 2 characters long';
            }
            if (!/^[a-zA-Z\s]+$/.test(value.trim())) {
              return 'Last name should only contain letters and spaces';
            }
            return null;
          }

          function validateMobileNumber(value) {
            if (!value || value.trim().length === 0) {
              return 'Mobile number is required';
            }
            const cleanNumber = value.replace(/\D/g, '');
            if (cleanNumber.length !== 10) {
              return 'Mobile number must be exactly 10 digits';
            }
            if (!/^[6-9]/.test(cleanNumber)) {
              return 'Mobile number must start with 6, 7, 8, or 9';
            }
            return null;
          }

          function validateEmail(value) {
            if (!value || value.trim().length === 0) {
              return 'Email address is required';
            }
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value.trim())) {
              return 'Please enter a valid email address';
            }
            return null;
          }

          function showFieldError(fieldId, message) {
            const input = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');

            input.classList.add('error');
            input.classList.remove('success');
            errorElement.textContent = message;
            errorElement.classList.add('show');
          }

          function showFieldSuccess(fieldId) {
            const input = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');

            input.classList.add('success');
            input.classList.remove('error');
            errorElement.classList.remove('show');
          }

          function validateField(fieldId, validator) {
            const input = document.getElementById(fieldId);
            const value = input.value;
            const error = validator(value);

            if (error) {
              showFieldError(fieldId, error);
              return false;
            } else {
              showFieldSuccess(fieldId);
              return true;
            }
          }

          // Setup form validation and submission
          document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('contactDetailsForm');

            // Real-time validation
            document.getElementById('firstName').addEventListener('blur', function() {
              validateField('firstName', validateFirstName);
            });

            document.getElementById('lastName').addEventListener('blur', function() {
              validateField('lastName', validateLastName);
            });

            document.getElementById('mobileNumber').addEventListener('input', function() {
              // Allow only numbers
              this.value = this.value.replace(/\D/g, '');
            });

            document.getElementById('mobileNumber').addEventListener('blur', function() {
              validateField('mobileNumber', validateMobileNumber);
            });

            document.getElementById('emailId').addEventListener('blur', function() {
              validateField('emailId', validateEmail);
            });

            // Form submission
            form.addEventListener('submit', function(e) {
              e.preventDefault();
              handleContactFormSubmission();
            });
          });

          async function handleContactFormSubmission() {
            const form = document.getElementById('contactDetailsForm');
            const submitBtn = form.querySelector('.submit-btn');

            // Validate all fields
            const isFirstNameValid = validateField('firstName', validateFirstName);
            const isLastNameValid = validateField('lastName', validateLastName);
            const isMobileValid = validateField('mobileNumber', validateMobileNumber);
            const isEmailValid = validateField('emailId', validateEmail);

            if (!isFirstNameValid || !isLastNameValid || !isMobileValid || !isEmailValid) {
              notificationManager.show('Please fix the errors in the form', 'error');
              return;
            }

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');

            try {
              // Collect form data
              const contactData = {
                firstName: document.getElementById('firstName').value.trim(),
                lastName: document.getElementById('lastName').value.trim(),
                mobileNumber: document.getElementById('mobileNumber').value.trim(),
                emailId: document.getElementById('emailId').value.trim(),
                packageId: selectedPackageId
              };

              // Use stored package context data
              const packageContext = selectedPackageContext || {};
              const selectedPackage = packageContext.selectedPackage || currentPackages.find(pkg => pkg.id === selectedPackageId);
              const searchParams = packageContext.searchParams || {
                destination: document.getElementById('destination')?.value || '',
                travel_date: document.getElementById('travelMonth')?.value || '',
                adults: travelers.adults,
                children: travelers.children,
                child: travelers.child,
                infants: travelers.infants
              };

              // Get selected EMI plan if any
              const selectedEmiPlan = selectedPackage?.emi_options?.find(emi => emi.selected) ||
                                   selectedPackage?.emi_options?.[0] || null;

              // Prepare API payload
              const apiPayload = {
                ...contactData,
                packageData: selectedPackage,
                searchParams: searchParams,
                selectedEmiPlan: selectedEmiPlan,
                utm_source: 'family_website_contact_form',
                session_id: `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
              };

              console.log('📤 Submitting contact details:', apiPayload);

              // Use global API configuration
              const apiUrl = API_CONFIG.getApiUrl('submit-contact-details');
              console.log('🌐 Using API URL:', apiUrl);

              const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(apiPayload)
              });

              const result = await response.json();

              if (!response.ok) {
                throw new Error(result.error || 'Failed to submit contact details');
              }

              if (result.success) {
                console.log('✅ Contact details submitted successfully:', result);

                // Store submission data for payment page
                const paymentData = {
                  quote_id: result.quote_id,
                  customer_name: result.customer_name,
                  customer_email: contactData.emailId,
                  customer_phone: contactData.mobileNumber,
                  package_data: selectedPackage,
                  search_params: searchParams,
                  selected_emi_plan: selectedEmiPlan,
                  submission_timestamp: new Date().toISOString()
                };

                // Store in session storage for payment page
                sessionStorage.setItem('tripxplo_payment_data', JSON.stringify(paymentData));

                // Show success message
                notificationManager.show('Contact details submitted! Redirecting to payment...', 'success');

                // Close the contact modal
                closeContactDetailsModal();

                // Redirect to payment page after a short delay
                setTimeout(() => {
                  openPaymentModal(paymentData);
                }, 1500);

              } else {
                throw new Error(result.error || 'Failed to submit contact details');
              }

            } catch (error) {
              console.error('❌ Error submitting contact details:', error);

              // Show user-friendly error message
              let errorMessage = 'Error submitting contact details. Please try again.';
              if (error.message.includes('Invalid email')) {
                errorMessage = 'Please enter a valid email address.';
              } else if (error.message.includes('Invalid mobile')) {
                errorMessage = 'Please enter a valid 10-digit mobile number.';
              } else if (error.message.includes('required')) {
                errorMessage = 'Please fill in all required fields.';
              }

              notificationManager.show(errorMessage, 'error');
            } finally {
              // Reset button state
              submitBtn.disabled = false;
              submitBtn.classList.remove('loading');
            }
          }

          function proceedWithBooking(contactData) {
            // This function handles what happens after successful contact submission
            console.log('✅ Contact details successfully stored in database:', contactData);

            // Show personalized success message
            const firstName = contactData.firstName || contactData.customer_name?.split(' ')[0] || 'Customer';
            notificationManager.show(
              `Thank you ${firstName}! Your booking request has been submitted. Our team will contact you within 24 hours.`,
              'success',
              6000
            );

            // Store the quote ID for future reference
            if (contactData.quote_id) {
              sessionStorage.setItem('latest_quote_id', contactData.quote_id);
              console.log('📝 Quote ID stored:', contactData.quote_id);
            }

            // Optional: Open the package modal for more details after a delay
            if (selectedPackageId) {
              setTimeout(() => {
                // Show additional information about next steps
                notificationManager.show(
                  'You can view your package details below. Our travel expert will call you soon!',
                  'info',
                  4000
                );

                // Open package modal to show details
                openPackageModal(selectedPackageId);
              }, 2000);
            }

            // Track the successful submission for analytics
            if (typeof gtag !== 'undefined') {
              gtag('event', 'contact_form_submission', {
                'event_category': 'engagement',
                'event_label': contactData.packageId || 'unknown_package',
                'value': 1
              });
            }

            // Optional: Scroll to top for better UX
            setTimeout(() => {
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }, 3000);
          }

          // Payment Modal Functions
          function openPaymentModal(paymentData) {
            console.log('🎯 Opening payment modal with data:', paymentData);

            // Populate payment modal with data
            populatePaymentModal(paymentData);

            // Show the modal
            const modal = document.getElementById('paymentModal');
            modal.style.display = 'flex';

            // Add animation
            setTimeout(() => {
              modal.classList.add('show');
            }, 10);
          }

          function closePaymentModal() {
            const modal = document.getElementById('paymentModal');
            modal.classList.remove('show');

            setTimeout(() => {
              modal.style.display = 'none';
            }, 300);
          }

          function populatePaymentModal(data) {
            const packageData = data.package_data || {};
            const searchParams = data.search_params || {};
            const emiPlan = data.selected_emi_plan || {};

            // Package Summary
            document.getElementById('paymentPackageTitle').textContent = packageData.title || packageData.destination || 'Travel Package';
            document.getElementById('paymentDestination').textContent = packageData.destination || searchParams.destination || 'Destination';
            document.getElementById('paymentDuration').textContent = `${packageData.duration_days || 5} Days / ${(packageData.duration_days || 5) - 1} Nights`;

            // Travelers info
            const adults = searchParams.adults || 2;
            const children = (searchParams.children || 0) + (searchParams.child || 0);
            const infants = searchParams.infants || 0;
            let travelersText = `${adults} Adult${adults > 1 ? 's' : ''}`;
            if (children > 0) travelersText += `, ${children} Child${children > 1 ? 'ren' : ''}`;
            if (infants > 0) travelersText += `, ${infants} Infant${infants > 1 ? 's' : ''}`;
            document.getElementById('paymentTravelers').textContent = travelersText;

            // Package cost
            const totalCost = packageData.total_price || packageData.price || 45000;
            document.getElementById('paymentTotalCost').textContent = `₹${totalCost.toLocaleString()}`;

            // Customer Details
            document.getElementById('paymentCustomerName').textContent = data.customer_name || 'Customer';
            document.getElementById('paymentCustomerEmail').textContent = data.customer_email || '';
            document.getElementById('paymentCustomerPhone').textContent = `+91 ${data.customer_phone || ''}`;

            // EMI Plan Details
            if (emiPlan && emiPlan.months) {
              const monthlyAmount = emiPlan.monthly_amount || Math.ceil(totalCost / emiPlan.months);
              // For prepaid EMI, no processing fee should be included in package cost
              const processingFee = 0; // Set to 0 for prepaid EMI
              const totalAmount = monthlyAmount * emiPlan.months; // Remove processing fee from total
              const isCustomEmi = emiPlan.is_custom;
              const emiTypeText = isCustomEmi ? `Custom ${emiPlan.months}` : emiPlan.months;

              document.getElementById('paymentEmiAmount').textContent = `₹${monthlyAmount.toLocaleString()}`;
              document.getElementById('paymentEmiMonths').textContent = emiTypeText;
              document.getElementById('paymentMonthlyEmi').textContent = `₹${monthlyAmount.toLocaleString()}`;
              document.getElementById('paymentProcessingFee').textContent = '₹0 (Prepaid EMI)';
              document.getElementById('paymentTotalAmount').textContent = `₹${totalAmount.toLocaleString()}`;
              document.getElementById('payNowAmount').textContent = `₹${monthlyAmount.toLocaleString()}`;

              // Generate payment schedule
              generatePaymentSchedule(emiPlan.months, monthlyAmount, processingFee, isCustomEmi);
            } else {
              // No EMI plan, show full payment
              document.getElementById('paymentEmiAmount').textContent = `₹${totalCost.toLocaleString()}`;
              document.getElementById('paymentEmiMonths').textContent = '1';
              document.getElementById('paymentMonthlyEmi').textContent = `₹${totalCost.toLocaleString()}`;
              document.getElementById('paymentProcessingFee').textContent = '₹0';
              document.getElementById('paymentTotalAmount').textContent = `₹${totalCost.toLocaleString()}`;
              document.getElementById('payNowAmount').textContent = `₹${totalCost.toLocaleString()}`;
            }
          }

          function generatePaymentSchedule(months, monthlyAmount, processingFee, isCustomEmi = false) {
            const scheduleContent = document.getElementById('paymentScheduleContent');
            let scheduleHTML = '<div class="schedule-list">';

            const today = new Date();
            const emiLabel = isCustomEmi ? 'Custom Prepaid EMI' : 'Prepaid EMI';

            // First payment (today) - for prepaid EMI, no processing fee
            const firstPayment = monthlyAmount; // No processing fee for prepaid EMI
            scheduleHTML += `
              <div class="schedule-item first-payment">
                <div class="payment-date">${today.toLocaleDateString('en-IN', { day: '2-digit', month: 'short', year: 'numeric' })}</div>
                <div class="payment-description">1st ${emiLabel}</div>
                <div class="payment-amount">₹${String(firstPayment).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}</div>
              </div>
            `;

            // Subsequent payments
            for (let i = 2; i <= months; i++) {
              const paymentDate = new Date(today);
              paymentDate.setMonth(paymentDate.getMonth() + (i - 1));

              scheduleHTML += `
                <div class="schedule-item">
                  <div class="payment-date">${paymentDate.toLocaleDateString('en-IN', { day: '2-digit', month: 'short', year: 'numeric' })}</div>
                  <div class="payment-description">${i}${getOrdinalSuffix(i)} ${emiLabel}</div>
                  <div class="payment-amount">₹${String(monthlyAmount).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}</div>
                </div>
              `;
            }

            scheduleHTML += '</div>';
            scheduleContent.innerHTML = scheduleHTML;
          }

          function getOrdinalSuffix(num) {
            const j = num % 10;
            const k = num % 100;
            if (j == 1 && k != 11) return "st";
            if (j == 2 && k != 12) return "nd";
            if (j == 3 && k != 13) return "rd";
            return "th";
          }

          function togglePaymentSchedule() {
            const content = document.getElementById('paymentScheduleContent');
            const toggle = document.querySelector('.toggle-schedule');

            if (content.classList.contains('expanded')) {
              content.classList.remove('expanded');
              toggle.classList.remove('expanded');
            } else {
              content.classList.add('expanded');
              toggle.classList.add('expanded');
            }
          }

          function editCustomerDetails() {
            closePaymentModal();
            openContactDetailsModal();
          }

          async function processPayment() {
            const agreeTerms = document.getElementById('agreeTerms').checked;

            if (!agreeTerms) {
              notificationManager.show('Please agree to the Terms & Conditions to proceed', 'warning');
              return;
            }

            const activeMethod = document.querySelector('.payment-method.active');
            if (!activeMethod) {
              notificationManager.show('Please select a payment method', 'warning');
              return;
            }

            const paymentMethod = activeMethod.dataset.method;

            // Get payment data from session storage
            const paymentData = JSON.parse(sessionStorage.getItem('tripxplo_payment_data') || '{}');

            if (!paymentData.quote_id) {
              notificationManager.show('Payment session expired. Please start again.', 'error');
              closePaymentModal();
              return;
            }

            // Show processing state
            const payBtn = document.querySelector('.pay-now-btn');
            payBtn.disabled = true;
            payBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing Payment...';

            try {
              // Calculate payment amount (first EMI only - no processing fee for prepaid EMI)
              const emiPlan = paymentData.selected_emi_plan || {};
              const monthlyAmount = emiPlan.monthly_amount || 0;
              const processingFee = 0; // No processing fee for prepaid EMI
              const paymentAmount = monthlyAmount; // Only monthly amount, no processing fee

              // Generate payment reference
              const paymentReference = `PAY_${Date.now()}_${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
              const gatewayReference = `GW_${Date.now()}_${paymentMethod.toUpperCase()}`;

              // Prepare payment payload
              const paymentPayload = {
                quote_id: paymentData.quote_id,
                payment_method: paymentMethod,
                payment_amount: paymentAmount,
                emi_plan: emiPlan,
                customer_data: {
                  name: paymentData.customer_name,
                  email: paymentData.customer_email,
                  phone: paymentData.customer_phone
                },
                package_data: paymentData.package_data,
                payment_reference: paymentReference,
                gateway_reference: gatewayReference
              };

              console.log('💳 Processing payment:', paymentPayload);

              // Use global API configuration for payment processing
              const paymentApiUrl = API_CONFIG.getApiUrl('process-payment');
              console.log('💳 Using Payment API URL:', paymentApiUrl);

              // Call payment processing API
              const response = await fetch(paymentApiUrl, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(paymentPayload)
              });

              const result = await response.json();

              if (!response.ok) {
                throw new Error(result.error || 'Payment processing failed');
              }

              if (result.success) {
                console.log('✅ Payment processed successfully:', result);

                // Store transaction details
                sessionStorage.setItem('tripxplo_transaction', JSON.stringify(result.data));

                // Show success message
                notificationManager.show('Payment processed successfully! Booking confirmed.', 'success');

                // Close payment modal
                closePaymentModal();

                // Show booking confirmation details
                setTimeout(() => {
                  showBookingConfirmation(result.data);
                }, 1000);

                // Show additional confirmation
                setTimeout(() => {
                  notificationManager.show('Confirmation details sent to your email and SMS!', 'info');
                }, 3000);

              } else {
                throw new Error(result.error || 'Payment processing failed');
              }

            } catch (error) {
              console.error('❌ Payment processing error:', error);

              // Show user-friendly error message
              let errorMessage = 'Payment processing failed. Please try again.';
              if (error.message.includes('network') || error.message.includes('fetch')) {
                errorMessage = 'Network error. Please check your connection and try again.';
              } else if (error.message.includes('session')) {
                errorMessage = 'Payment session expired. Please start the booking process again.';
              } else if (error.message.includes('Payment tables not found')) {
                errorMessage = 'Payment system is being set up. Please contact support or try again later.';
                console.error('🔧 Database setup required: Payment tables not found');
              }

              notificationManager.show(errorMessage, 'error');

            } finally {
              // Reset button state
              payBtn.disabled = false;
              payBtn.innerHTML = '<i class="fas fa-lock"></i> Pay Now - <span id="payNowAmount">₹7,500</span>';
            }
          }

          function showBookingConfirmation(transactionData) {
            const confirmationMessage = `
              🎉 Booking Confirmed!

              Booking Reference: ${transactionData.booking_reference}
              Payment Amount: ₹${transactionData.payment_amount.toLocaleString()}
              Remaining Amount: ₹${transactionData.remaining_amount.toLocaleString()}
              Next EMI Due: ${transactionData.next_emi_due_date}
              Remaining EMIs: ${transactionData.remaining_months}

              Transaction ID: ${transactionData.transaction_id}
            `;

            // You can replace this with a proper modal or redirect to confirmation page
            alert(confirmationMessage);

            // Optional: Redirect to booking confirmation page
            // window.location.href = `/booking-confirmation?ref=${transactionData.booking_reference}`;
          }

          // Payment method selection
          document.addEventListener('DOMContentLoaded', function() {
            // Payment method selection
            document.querySelectorAll('.payment-method').forEach(method => {
              method.addEventListener('click', function() {
                // Remove active class from all methods
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('active'));

                // Add active class to clicked method
                this.classList.add('active');

                // Show corresponding form
                const methodType = this.dataset.method;
                document.querySelectorAll('.payment-form-content').forEach(form => {
                  form.classList.remove('active');
                });
                document.getElementById(`${methodType}PaymentForm`).classList.add('active');
              });
            });

            // Card number formatting
            const cardNumberInput = document.getElementById('cardNumber');
            if (cardNumberInput) {
              cardNumberInput.addEventListener('input', function() {
                let value = this.value.replace(/\s/g, '').replace(/\D/g, '');
                value = value.substring(0, 16);
                value = value.replace(/(.{4})/g, '$1 ').trim();
                this.value = value;
              });
            }

            // Expiry date formatting
            const expiryInput = document.getElementById('expiryDate');
            if (expiryInput) {
              expiryInput.addEventListener('input', function() {
                let value = this.value.replace(/\D/g, '');
                if (value.length >= 2) {
                  value = value.substring(0, 2) + '/' + value.substring(2, 4);
                }
                this.value = value;
              });
            }

            // CVV formatting
            const cvvInput = document.getElementById('cvv');
            if (cvvInput) {
              cvvInput.addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 4);
              });
            }
          });

          function showTermsModal() {
            notificationManager.show('Terms & Conditions modal would open here', 'info');
          }

          function showPrivacyModal() {
            notificationManager.show('Privacy Policy modal would open here', 'info');
          }

          // ============================================================================
          // AUTHENTICATION FUNCTIONS
          // ============================================================================

          // Open authentication modal (also defined globally above)
          function openAuthModal() {
            console.log('Local openAuthModal called');
            document.getElementById('authModal').style.display = 'flex';
            resetAuthModal();
          }

          // Close authentication modal
          function closeAuthModal() {
            document.getElementById('authModal').style.display = 'none';
            resetAuthModal();
          }

          // Reset authentication modal to initial state
          function resetAuthModal() {
            // Show login step by default
            showAuthStep('login');

            // Clear all form inputs
            clearAllAuthInputs();

            // Clear error messages
            clearAllAuthErrors();

            // Reset auth state
            authState.currentStep = 'login';
            authState.signupData = { name: '', email: '', mobileNumber: '' };
          }

          // Show specific authentication step
          function showAuthStep(step) {
            // Hide all steps
            document.getElementById('loginStep').style.display = 'none';
            document.getElementById('signupStep1').style.display = 'none';
            document.getElementById('signupStep2').style.display = 'none';
            document.getElementById('authLoading').style.display = 'none';

            // Show requested step
            switch(step) {
              case 'login':
                document.getElementById('loginStep').style.display = 'block';
                document.getElementById('authModalTitle').innerHTML = '<i class="fas fa-mobile-alt"></i> Login to TripXplo';
                break;
              case 'signup1':
                document.getElementById('signupStep1').style.display = 'block';
                document.getElementById('authModalTitle').innerHTML = '<i class="fas fa-user-plus"></i> Create Account';
                break;
              case 'signup2':
                document.getElementById('signupStep2').style.display = 'block';
                document.getElementById('authModalTitle').innerHTML = '<i class="fas fa-lock"></i> Setup PIN';
                break;
            }

            authState.currentStep = step;
          }

          // Clear all form inputs
          function clearAllAuthInputs() {
            // Login form
            document.getElementById('loginMobileNumber').value = '';
            document.getElementById('loginPin').value = '';

            // Signup form 1
            document.getElementById('signupName').value = '';
            document.getElementById('signupEmail').value = '';
            document.getElementById('signupMobileNumber').value = '';

            // Signup form 2
            document.getElementById('signupPin').value = '';
            document.getElementById('signupConfirmPin').value = '';
          }

          // Clear all error messages
          function clearAllAuthErrors() {
            const errorElements = [
              'loginMobileError', 'loginPinError',
              'signupNameError', 'signupEmailError', 'signupMobileError',
              'signupPinError', 'signupConfirmPinError'
            ];

            errorElements.forEach(id => {
              const element = document.getElementById(id);
              if (element) element.textContent = '';
            });
          }

          // Switch between login and signup
          function switchToSignup() {
            showAuthStep('signup1');
          }

          function switchToLogin() {
            showAuthStep('login');
          }

          function goBackToSignupDetails() {
            showAuthStep('signup1');
          }

          // Setup form event listeners
          document.addEventListener('DOMContentLoaded', function() {
            // Login form
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
              loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                await handleLogin();
              });
            }

            // Signup form 1
            const signupForm1 = document.getElementById('signupForm1');
            if (signupForm1) {
              signupForm1.addEventListener('submit', async function(e) {
                e.preventDefault();
                await handleSignupStep1();
              });
            }

            // Signup form 2
            const signupForm2 = document.getElementById('signupForm2');
            if (signupForm2) {
              signupForm2.addEventListener('submit', async function(e) {
                e.preventDefault();
                await handleSignupStep2();
              });
            }

            // Input formatting for mobile numbers
            const mobileInputs = ['loginMobileNumber', 'signupMobileNumber'];
            mobileInputs.forEach(id => {
              const input = document.getElementById(id);
              if (input) {
                input.addEventListener('input', function() {
                  this.value = this.value.replace(/\D/g, '').substring(0, 10);
                });
              }
            });

            // Input formatting for PINs
            const pinInputs = ['loginPin', 'signupPin', 'signupConfirmPin'];
            pinInputs.forEach(id => {
              const input = document.getElementById(id);
              if (input) {
                input.addEventListener('input', function() {
                  this.value = this.value.replace(/\D/g, '').substring(0, 4);
                });
              }
            });
          });

          // Handle login form submission
          async function handleLogin() {
            const mobileNumber = document.getElementById('loginMobileNumber').value.trim();
            const pin = document.getElementById('loginPin').value.trim();

            // Clear previous errors
            clearAllAuthErrors();

            // Validate inputs
            if (!validateAuthMobileNumber(mobileNumber)) {
              showErrorPopup('Please enter a valid 10-digit mobile number');
              return;
            }

            if (!validatePin(pin)) {
              showErrorPopup('Please enter a 4-digit PIN');
              return;
            }

            showAuthLoading('Logging you in...');

            try {
              const response = await fetch(API_CONFIG.getApiUrl('login'), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mobileNumber, pin })
              });

              const result = await response.json();

              if (result.success) {
                handleAuthSuccess(result.user, false);
              } else {
                hideAuthLoading();
                // Show specific error messages for login failures
                let errorMessage = 'Login failed. Please try again.';
                if (result.error) {
                  if (result.error.includes('User not found') || result.error.includes('does not exist')) {
                    errorMessage = 'Mobile number not registered. Please sign up first.';
                  } else if (result.error.includes('Invalid PIN') || result.error.includes('incorrect')) {
                    errorMessage = 'Incorrect PIN. Please check and try again.';
                  } else if (result.error.includes('locked') || result.error.includes('attempts')) {
                    errorMessage = 'Account temporarily locked due to multiple failed attempts.';
                  } else {
                    errorMessage = result.error;
                  }
                }
                showErrorPopup(errorMessage);
              }
            } catch (error) {
              console.error('Error during login:', error);
              hideAuthLoading();
              showErrorPopup('Login failed. Please check your connection and try again.');
            }
          }

          // Handle signup step 1 (user details)
          async function handleSignupStep1() {
            const name = document.getElementById('signupName').value.trim();
            const email = document.getElementById('signupEmail').value.trim();
            const mobileNumber = document.getElementById('signupMobileNumber').value.trim();

            // Clear previous errors
            clearAllAuthErrors();

            // Validate inputs
            if (!name) {
              document.getElementById('signupNameError').textContent = 'Please enter your full name';
              return;
            }

            if (!validateAuthEmail(email)) {
              document.getElementById('signupEmailError').textContent = 'Please enter a valid email address';
              return;
            }

            if (!validateAuthMobileNumber(mobileNumber)) {
              document.getElementById('signupMobileError').textContent = 'Please enter a valid 10-digit mobile number';
              return;
            }

            showAuthLoading('Checking mobile number availability...');

            try {
              // Check if user already exists
              const response = await fetch(API_CONFIG.getApiUrl('check-user'), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mobileNumber })
              });

              const result = await response.json();

              if (result.success) {
                if (result.userExists) {
                  hideAuthLoading();
                  showErrorPopup('An account with this mobile number already exists. Please login instead.');
                  return;
                }

                // Store signup data and proceed to PIN setup
                authState.signupData = { name, email, mobileNumber };

                // Update user display
                document.getElementById('displayUserInfo').textContent = `${name} - ${email}`;

                hideAuthLoading();
                showAuthStep('signup2');
              } else {
                throw new Error(result.error || 'Failed to verify mobile number');
              }
            } catch (error) {
              console.error('Error checking user:', error);
              hideAuthLoading();
              showErrorPopup('Unable to verify mobile number. Please try again.');
            }
          }

          // Handle signup step 2 (PIN setup)
          async function handleSignupStep2() {
            const pin = document.getElementById('signupPin').value.trim();
            const confirmPin = document.getElementById('signupConfirmPin').value.trim();

            // Clear previous errors
            clearAllAuthErrors();

            // Validate PIN
            if (!validatePin(pin)) {
              document.getElementById('signupPinError').textContent = 'Please enter a 4-digit PIN';
              return;
            }

            if (pin !== confirmPin) {
              document.getElementById('signupConfirmPinError').textContent = 'PINs do not match';
              return;
            }

            showAuthLoading('Creating your account...');

            try {
              const response = await fetch(API_CONFIG.getApiUrl('signup'), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  name: authState.signupData.name,
                  email: authState.signupData.email,
                  mobileNumber: authState.signupData.mobileNumber,
                  pin: pin
                })
              });

              const result = await response.json();

              if (result.success) {
                handleAuthSuccess(result.user, true);
              } else {
                hideAuthLoading();
                showErrorPopup(result.error || 'Failed to create account');
              }
            } catch (error) {
              console.error('Error during signup:', error);
              hideAuthLoading();
              showErrorPopup('Account creation failed. Please try again.');
            }
          }



          // Show error popup
          function showErrorPopup(message) {
            // Create error popup
            const popup = document.createElement('div');
            popup.className = 'error-popup';
            popup.innerHTML = `
              <div class="error-popup-content">
                <div class="error-icon">
                  <i class="fas fa-exclamation-triangle"></i>
                </div>
                <p class="error-message">${message}</p>
                <button class="error-close-btn" onclick="closeErrorPopup(this)">
                  <i class="fas fa-times"></i> Close
                </button>
              </div>
            `;

            document.body.appendChild(popup);

            // Auto-remove after 5 seconds
            setTimeout(() => {
              if (popup.parentNode) {
                popup.parentNode.removeChild(popup);
              }
            }, 5000);
          }

          // Close error popup
          function closeErrorPopup(button) {
            const popup = button.closest('.error-popup');
            if (popup && popup.parentNode) {
              popup.parentNode.removeChild(popup);
            }
          }

          // Authentication validation functions
          function validateAuthMobileNumber(mobile) {
            return /^[6-9]\d{9}$/.test(mobile);
          }

          function validatePin(pin) {
            return /^\d{4}$/.test(pin);
          }

          function validateAuthEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
          }

          // Show/hide loading state
          function showAuthLoading(message) {
            // Hide all auth steps
            document.getElementById('loginStep').style.display = 'none';
            document.getElementById('signupStep1').style.display = 'none';
            document.getElementById('signupStep2').style.display = 'none';

            // Show loading
            document.getElementById('authLoading').style.display = 'block';
            document.getElementById('loadingMessage').textContent = message;
          }

          function hideAuthLoading() {
            document.getElementById('authLoading').style.display = 'none';

            // Show the appropriate step based on current state
            switch(authState.currentStep) {
              case 'login':
                document.getElementById('loginStep').style.display = 'block';
                break;
              case 'signup1':
                document.getElementById('signupStep1').style.display = 'block';
                break;
              case 'signup2':
                document.getElementById('signupStep2').style.display = 'block';
                break;
              default:
                document.getElementById('loginStep').style.display = 'block';
                authState.currentStep = 'login';
            }
          }



          // Toggle PIN visibility functions
          function toggleLoginPinVisibility() {
            const pinInput = document.getElementById('loginPin');
            const icon = document.getElementById('loginPinVisibilityIcon');

            if (pinInput.type === 'password') {
              pinInput.type = 'text';
              icon.className = 'fas fa-eye-slash';
            } else {
              pinInput.type = 'password';
              icon.className = 'fas fa-eye';
            }
          }

          function toggleSignupPinVisibility() {
            const pinInput = document.getElementById('signupPin');
            const icon = document.getElementById('signupPinVisibilityIcon');

            if (pinInput.type === 'password') {
              pinInput.type = 'text';
              icon.className = 'fas fa-eye-slash';
            } else {
              pinInput.type = 'password';
              icon.className = 'fas fa-eye';
            }
          }

          // Update UI after authentication
          function updateAuthUI() {
            const authBtn = document.getElementById('authBtn');
            if (authState.isAuthenticated && authState.user) {
              // Show user's name instead of mobile number
              const displayName = authState.user.name || 'User';
              authBtn.innerHTML = `<i class="fas fa-user-circle"></i> ${displayName}`;
              authBtn.onclick = showUserMenu;
            } else {
              authBtn.innerHTML = '<i class="fas fa-user"></i> Signup/Login';
              authBtn.onclick = openAuthModal;
            }
          }



          // Logout function
          function logout() {
            authState.isAuthenticated = false;
            authState.user = null;
            updateAuthUI();
            notificationManager.show('Logged out successfully', 'success');
          }

          // Check authentication status on page load
          document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in (from localStorage or session)
            checkAuthStatus();
          });

          async function checkAuthStatus() {
            try {
              // Check if there's a stored session
              const storedAuth = localStorage.getItem('tripxplo_auth');
              if (storedAuth) {
                const authData = JSON.parse(storedAuth);

                // Check if session is still valid
                if (authData.isAuthenticated && authData.user && authData.sessionExpiry) {
                  const expiryDate = new Date(authData.sessionExpiry);
                  const now = new Date();

                  if (expiryDate > now) {
                    // Session is still valid
                    authState.isAuthenticated = true;
                    authState.user = authData.user;
                    updateAuthUI();
                    console.log('✅ Valid session found, user logged in');
                  } else {
                    // Session expired
                    console.log('⏰ Session expired, clearing auth data');
                    clearAuthData();
                  }
                } else {
                  // Invalid auth data
                  clearAuthData();
                }
              }
            } catch (error) {
              console.error('Error checking auth status:', error);
              clearAuthData();
            }
          }

          // Save authentication data to localStorage
          function saveAuthData(userData) {
            try {
              const authData = {
                isAuthenticated: true,
                user: userData,
                sessionExpiry: userData.sessionExpiry,
                lastLogin: new Date().toISOString()
              };
              localStorage.setItem('tripxplo_auth', JSON.stringify(authData));
              console.log('✅ Auth data saved to localStorage');
            } catch (error) {
              console.error('Error saving auth data:', error);
            }
          }

          // Clear authentication data
          function clearAuthData() {
            try {
              localStorage.removeItem('tripxplo_auth');
              authState.isAuthenticated = false;
              authState.user = null;
              updateAuthUI();
              console.log('🗑️ Auth data cleared');
            } catch (error) {
              console.error('Error clearing auth data:', error);
            }
          }

          // Enhanced authentication success handler
          function handleAuthSuccess(userData, isNewUser = false) {
            // Update auth state
            authState.isAuthenticated = true;
            authState.user = userData;

            // Save to localStorage
            saveAuthData(userData);

            // Update UI
            updateAuthUI();

            // Close modal
            closeAuthModal();

            // Show success message
            notificationManager.show(
              isNewUser ? '🎉 Account created successfully! Welcome to TripXplo!' : '👋 Welcome back!',
              'success',
              5000
            );

            // Optional: Track user login for analytics
            trackUserLogin(userData, isNewUser);
          }

          // Track user login (placeholder for analytics)
          function trackUserLogin(userData, isNewUser) {
            try {
              // This could be connected to Google Analytics, Facebook Pixel, etc.
              console.log('📊 User login tracked:', {
                userId: userData.id,
                mobile: userData.mobile,
                isNewUser,
                timestamp: new Date().toISOString()
              });
            } catch (error) {
              console.error('Error tracking user login:', error);
            }
          }

          // Enhanced logout function
          function logout() {
            try {
              // Clear auth state
              authState.isAuthenticated = false;
              authState.user = null;

              // Clear localStorage
              clearAuthData();

              // Show success message
              notificationManager.show('👋 Logged out successfully', 'success');

              console.log('✅ User logged out successfully');
            } catch (error) {
              console.error('Error during logout:', error);
              notificationManager.show('Error during logout', 'error');
            }
          }

          // Enhanced user menu
          function showUserMenu() {
            const user = authState.user;
            const displayName = user?.name || 'User';
            const displayEmail = user?.email || '';
            const displayMobile = user?.mobile ? `+91 ${user.mobile}` : '';

            const userMenuHTML = `
              <div class="user-menu-dropdown" id="userMenuDropdown">
                <div class="user-menu-header">
                  <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                  </div>
                  <div class="user-info">
                    <div class="user-name">${displayName}</div>
                    <div class="user-contact">${displayEmail}</div>
                    <div class="user-mobile">${displayMobile}</div>
                  </div>
                </div>
                <div class="user-menu-items">
                  <button class="user-menu-item" onclick="showUserProfile(); closeUserMenu();">
                    <i class="fas fa-user-edit"></i>
                    <div class="menu-item-content">
                      <span class="menu-item-title">Profile</span>
                      <span class="menu-item-desc">Manage your account</span>
                    </div>
                  </button>
                  <button class="user-menu-item" onclick="showBookingHistory(); closeUserMenu();">
                    <i class="fas fa-suitcase-rolling"></i>
                    <div class="menu-item-content">
                      <span class="menu-item-title">My Bookings</span>
                      <span class="menu-item-desc">View your trips</span>
                    </div>
                  </button>
                  <div class="menu-divider"></div>
                  <button class="user-menu-item logout-item" onclick="logout(); closeUserMenu();">
                    <i class="fas fa-sign-out-alt"></i>
                    <div class="menu-item-content">
                      <span class="menu-item-title">Logout</span>
                      <span class="menu-item-desc">Sign out of your account</span>
                    </div>
                  </button>
                </div>
              </div>
            `;

            // Remove existing menu
            const existingMenu = document.getElementById('userMenuDropdown');
            if (existingMenu) {
              existingMenu.remove();
            }

            // Add new menu
            document.body.insertAdjacentHTML('beforeend', userMenuHTML);

            // Position menu
            const authBtn = document.getElementById('authBtn');
            const menu = document.getElementById('userMenuDropdown');
            const btnRect = authBtn.getBoundingClientRect();

            menu.style.position = 'fixed';
            menu.style.top = (btnRect.bottom + 10) + 'px';
            menu.style.right = '20px';
            menu.style.zIndex = '10000';

            // Close menu when clicking outside
            setTimeout(() => {
              document.addEventListener('click', function closeMenuOnOutsideClick(e) {
                if (!menu.contains(e.target) && !authBtn.contains(e.target)) {
                  closeUserMenu();
                  document.removeEventListener('click', closeMenuOnOutsideClick);
                }
              });
            }, 100);
          }

          function closeUserMenu() {
            const menu = document.getElementById('userMenuDropdown');
            if (menu) {
              menu.remove();
            }
          }

          // Make closeUserMenu globally accessible
          window.closeUserMenu = closeUserMenu;

          // Show user profile modal
          function showUserProfile() {
            console.log('showUserProfile called');
            console.log('authState:', authState);

            // Check if notificationManager is available
            if (typeof notificationManager === 'undefined') {
              console.error('notificationManager is not defined');
              alert('Please log in to view your profile');
              return;
            }

            const user = authState.user;
            if (!user) {
              console.log('No user found in authState');
              notificationManager.show('Please log in to view your profile', 'warning');
              return;
            }

            console.log('User found:', user);

            const profileModalHTML = `
              <div class="modal-overlay" id="profileModal">
                <div class="modal-content profile-modal-enhanced">
                  <div class="profile-header-section">
                    <div class="profile-header-bg"></div>
                    <button class="modal-close" onclick="closeProfileModal()">
                      <i class="fas fa-times"></i>
                    </button>
                    <div class="profile-header-content">
                      <div class="profile-avatar-container">
                        <div class="profile-avatar-large">
                          <i class="fas fa-user"></i>
                        </div>
                      </div>
                      <div class="profile-header-info">
                        <h2 class="profile-name">${user.name || 'User'}</h2>
                        <p class="profile-email">${user.email || '<EMAIL>'}</p>
                      </div>
                    </div>
                  </div>

                  <div class="modal-body profile-body-enhanced">
                    <!-- Profile Form Section -->
                    <div class="profile-form-section">
                      <h4 class="section-title">
                        <i class="fas fa-user-edit"></i>
                        Personal Information
                      </h4>
                      <form id="profileForm" class="profile-form-enhanced">
                        <div class="form-grid">
                          <div class="form-group-enhanced">
                            <label for="profileName" class="form-label-enhanced">
                              <i class="fas fa-user"></i>
                              Full Name
                            </label>
                            <div class="input-wrapper">
                              <input
                                type="text"
                                id="profileName"
                                name="name"
                                class="form-input-enhanced"
                                value="${user.name || ''}"
                                placeholder="Enter your full name"
                                required
                              />
                              <div class="input-focus-border"></div>
                            </div>
                          </div>

                          <div class="form-group-enhanced">
                            <label for="profileEmail" class="form-label-enhanced">
                              <i class="fas fa-envelope"></i>
                              Email Address
                            </label>
                            <div class="input-wrapper">
                              <input
                                type="email"
                                id="profileEmail"
                                name="email"
                                class="form-input-enhanced"
                                value="${user.email || ''}"
                                placeholder="Enter your email address"
                                required
                              />
                              <div class="input-focus-border"></div>
                            </div>
                          </div>

                          <div class="form-group-enhanced">
                            <label for="profileMobile" class="form-label-enhanced">
                              <i class="fas fa-mobile-alt"></i>
                              Mobile Number
                            </label>
                            <div class="input-wrapper">
                              <div class="mobile-input-enhanced">
                                <span class="country-code-enhanced">+91</span>
                                <input
                                  type="tel"
                                  id="profileMobile"
                                  name="mobile"
                                  class="form-input-enhanced mobile-input"
                                  value="${user.mobile || ''}"
                                  placeholder="Enter 10-digit number"
                                  maxlength="10"
                                  pattern="[0-9]{10}"
                                  required
                                />
                              </div>
                              <div class="input-focus-border"></div>
                            </div>
                            <small class="form-help-enhanced">
                              <i class="fas fa-info-circle"></i>
                              Used for booking confirmations and updates
                            </small>
                          </div>
                        </div>

                      </form>
                    </div>

                    <!-- Quick Actions Section -->
                    <div class="quick-actions-section">
                      <h4 class="section-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                      </h4>
                      <div class="quick-actions-grid">
                        <button class="quick-action-btn" onclick="closeProfileModal(); scrollToPackages();">
                          <i class="fas fa-search"></i>
                          <span>Browse Packages</span>
                        </button>
                        <button class="quick-action-btn" onclick="closeProfileModal(); showBookingHistory();">
                          <i class="fas fa-history"></i>
                          <span>View Bookings</span>
                        </button>
                        <button class="quick-action-btn">
                          <i class="fas fa-headset"></i>
                          <span>Contact Support</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="modal-footer profile-footer-enhanced">
                    <button type="button" class="btn-secondary-enhanced" onclick="closeProfileModal()">
                      <i class="fas fa-times"></i>
                      Cancel
                    </button>
                    <button type="submit" form="profileForm" class="btn-primary-enhanced">
                      <i class="fas fa-save"></i>
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            `;

            console.log('📝 Inserting profile modal HTML into DOM');
            document.body.insertAdjacentHTML('beforeend', profileModalHTML);

            // Check if modal was created and make it visible
            const createdModal = document.getElementById('profileModal');
            if (createdModal) {
              console.log('✅ Profile modal created successfully');
              console.log('Modal element:', createdModal);

              // Make modal visible
              createdModal.style.display = 'flex';
              console.log('✅ Profile modal set to display: flex');
              console.log('Modal display style:', window.getComputedStyle(createdModal).display);
            } else {
              console.error('❌ Profile modal was not created');
            }

            // Setup mobile number input formatting
            const profileMobileInput = document.getElementById('profileMobile');
            if (profileMobileInput) {
              profileMobileInput.addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 10);
              });
            }

            // Setup form submission
            const profileForm = document.getElementById('profileForm');
            if (profileForm) {
              profileForm.addEventListener('submit', handleProfileUpdate);
              console.log('✅ Profile form event listener added');
            } else {
              console.error('❌ Profile form not found');
            }
          }

          // Make profile function globally accessible
          window.showUserProfile = showUserProfile;

          // Close profile modal
          function closeProfileModal() {
            const modal = document.getElementById('profileModal');
            if (modal) {
              modal.remove();
            }
          }

          // Handle profile update
          async function handleProfileUpdate(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const name = formData.get('name').trim();
            const email = formData.get('email').trim();
            const mobile = formData.get('mobile').trim();

            // Validate inputs
            if (!name || !email || !mobile) {
              notificationManager.show('Please fill in all required fields', 'error');
              return;
            }

            if (!validateAuthEmail(email)) {
              notificationManager.show('Please enter a valid email address', 'error');
              return;
            }

            if (!validateAuthMobileNumber(mobile)) {
              notificationManager.show('Please enter a valid 10-digit mobile number', 'error');
              return;
            }

            try {
              // Here you would typically make an API call to update the profile
              // For now, we'll just update the local state
              authState.user.name = name;
              authState.user.email = email;
              authState.user.mobile = mobile;

              // Update localStorage
              saveAuthData(authState.user);

              // Update UI
              updateAuthUI();

              // Close modal and show success
              closeProfileModal();
              notificationManager.show('Profile updated successfully!', 'success');

            } catch (error) {
              console.error('Error updating profile:', error);
              notificationManager.show('Failed to update profile. Please try again.', 'error');
            }
          }

          // Show booking history modal
          async function showBookingHistory() {
            console.log('showBookingHistory called');
            console.log('authState:', authState);

            // Check if notificationManager is available
            if (typeof notificationManager === 'undefined') {
              console.error('notificationManager is not defined');
              alert('Please log in to view your bookings');
              return;
            }

            if (!authState.user) {
              console.log('No user found in authState');
              notificationManager.show('Please log in to view your bookings', 'warning');
              return;
            }

            console.log('User found, showing booking modal');
            // Show loading first
            const loadingModalHTML = `
              <div class="modal-overlay" id="bookingModal">
                <div class="modal-content booking-modal">
                  <div class="modal-header">
                    <h3><i class="fas fa-suitcase-rolling"></i> My Bookings</h3>
                    <button class="modal-close" onclick="closeBookingModal()">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                  <div class="modal-body">
                    <div class="loading-state">
                      <div class="loading-spinner"></div>
                      <p>Loading your bookings...</p>
                    </div>
                  </div>
                </div>
              </div>
            `;

            console.log('📝 Inserting booking modal HTML into DOM');
            document.body.insertAdjacentHTML('beforeend', loadingModalHTML);

            // Check if modal was created and make it visible
            const createdModal = document.getElementById('bookingModal');
            if (createdModal) {
              console.log('✅ Booking modal created successfully');
              console.log('Modal element:', createdModal);

              // Make modal visible
              createdModal.style.display = 'flex';
              console.log('✅ Booking modal set to display: flex');
              console.log('Modal display style:', window.getComputedStyle(createdModal).display);
            } else {
              console.error('❌ Booking modal was not created');
            }

            try {
              // Check if running from file:// URL
              if (window.location.protocol === 'file:') {
                console.log('Running from file://, using mock data');
                notificationManager.show('Demo mode: Showing sample booking data', 'info');
              }

              // Fetch user's bookings
              const bookings = await fetchUserBookings();
              console.log('📊 Fetched bookings:', bookings);

              // Update modal with booking data
              updateBookingModal(bookings);

            } catch (error) {
              console.error('Error fetching bookings:', error);
              updateBookingModalWithError();
            }
          }

          // Fetch user bookings from API with CORS error handling
          async function fetchUserBookings() {
            console.log('🔍 fetchUserBookings called');
            console.log('📱 User mobile:', authState.user?.mobile);

            if (!authState.user || !authState.user.mobile) {
              console.error('❌ User not authenticated');
              throw new Error('User not authenticated');
            }

            try {
              console.log('📡 Making API call to emi-transactions');
              // Try to fetch from prepaid_emi_transactions API
              const response = await fetch(API_CONFIG.getApiUrl('emi-transactions'), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${authState.user.sessionToken}`
                },
                body: JSON.stringify({
                  mobileNumber: authState.user.mobile
                })
              });

              console.log('📡 API Response status:', response.status);

              if (!response.ok) {
                throw new Error(`Failed to fetch EMI transactions: ${response.status}`);
              }

              const result = await response.json();
              console.log('📊 API Result:', result);

              // Transform EMI transactions to booking format
              if (result.success && result.transactions && result.transactions.length > 0) {
                console.log('✅ Found transactions, transforming...');
                const transformedBookings = result.transactions.map(transaction => {
                  console.log('🔄 Transforming transaction:', transaction);

                  // Calculate months information
                  const totalMonths = Math.ceil(transaction.total_emi_amount / transaction.monthly_emi_amount);
                  const remainingMonths = transaction.remaining_emi_months || 0;
                  const paidMonths = totalMonths - remainingMonths;

                  return {
                    reference_id: transaction.booking_reference,
                    package_name: `${transaction.destination || 'Travel'} Package`,
                    destination: transaction.destination || 'Travel Destination',
                    customer_name: transaction.customer_name || authState.user.name || 'User',
                    customer_phone: transaction.customer_phone || authState.user.mobile || '8072396488',
                    customer_email: transaction.customer_email || authState.user.email || '<EMAIL>',
                    payment_method: transaction.payment_method || 'card',
                    advance_amount: transaction.advance_payment_amount,
                    monthly_emi_amount: transaction.monthly_emi_amount,
                    total_emi_amount: transaction.total_emi_amount,
                    estimated_total_cost: transaction.total_emi_amount,
                    total_paid: transaction.total_paid_amount,
                    status: transaction.payment_status,
                    created_at: transaction.created_at,
                    next_due_date: transaction.next_emi_due_date,
                    travel_date: transaction.travel_date || '2025-12',
                    // Add months information
                    total_months: totalMonths,
                    paid_months: paidMonths,
                    remaining_months: remainingMonths
                  };
                });

                console.log('✅ Transformed bookings:', transformedBookings);
                return transformedBookings;
              } else {
                console.log('⚠️ No transactions found in API response');
                return [];
              }

            } catch (error) {
              console.warn('API call failed, using mock data:', error);

              // Return mock booking data when API fails (CORS or other issues)
              const mockTotalMonths = 6; // 6 months EMI
              const mockPaidMonths = 1; // 1 month paid (advance)
              const mockRemainingMonths = 5; // 5 months remaining

              return [{
                reference_id: 'TXP17504169493040ZAY',
                package_name: 'Goa Package',
                destination: 'Goa',
                customer_name: authState.user.name || 'User',
                customer_phone: authState.user.mobile || '8072396488',
                customer_email: authState.user.email || '<EMAIL>',
                payment_method: 'card',
                advance_amount: 7053,
                monthly_emi_amount: 5977,
                total_emi_amount: 36938,
                estimated_total_cost: 36938,
                total_paid: 7053,
                status: 'active',
                created_at: new Date().toISOString(),
                next_due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
                travel_date: '2025-12',
                // Add months information
                total_months: mockTotalMonths,
                paid_months: mockPaidMonths,
                remaining_months: mockRemainingMonths
              }];
            }
          }

          // Update booking modal with data
          function updateBookingModal(bookings) {
            const modal = document.getElementById('bookingModal');
            if (!modal) return;

            const modalBody = modal.querySelector('.modal-body');

            if (bookings.length === 0) {
              modalBody.innerHTML = `
                <div class="empty-state">
                  <i class="fas fa-calendar-plus"></i>
                  <h4>No Bookings Found</h4>
                  <p>You don't have any bookings yet. Start planning your next adventure!</p>
                  <button class="cta-btn" onclick="closeBookingModal(); scrollToPackages();">
                    <i class="fas fa-search"></i> Browse Packages
                  </button>
                </div>
              `;
              return;
            }

            // Generate booking cards
            const bookingCardsHTML = bookings.map(booking => generateBookingCard(booking)).join('');

            modalBody.innerHTML = `
              <div class="bookings-list">
                <div class="bookings-header">
                  <h4><i class="fas fa-list"></i> Your Bookings (${bookings.length})</h4>
                </div>
                <div class="bookings-container">
                  ${bookingCardsHTML}
                </div>
              </div>
            `;
          }

          // Generate individual booking card
          function generateBookingCard(booking) {
            const formatDate = (dateString) => {
              return new Date(dateString).toLocaleDateString('en-IN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
              });
            };

            const formatCurrency = (amount) => {
              return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                minimumFractionDigits: 0
              }).format(amount);
            };

            const getStatusBadge = (status) => {
              const statusMap = {
                'active': { class: 'status-active', icon: 'fas fa-clock', text: 'Active' },
                'completed': { class: 'status-completed', icon: 'fas fa-check-circle', text: 'Completed' },
                'cancelled': { class: 'status-cancelled', icon: 'fas fa-times-circle', text: 'Cancelled' }
              };
              const statusInfo = statusMap[status] || statusMap['active'];
              return `<span class="status-badge ${statusInfo.class}"><i class="${statusInfo.icon}"></i> ${statusInfo.text}</span>`;
            };

            return `
              <div class="booking-card-enhanced">
                <!-- Card Header -->
                <div class="booking-card-header-enhanced">
                  <div class="booking-reference-section">
                    <div class="reference-id">
                      <i class="fas fa-ticket-alt"></i>
                      <span class="reference-text">${booking.reference_id}</span>
                    </div>
                    ${getStatusBadge(booking.status)}
                  </div>
                  <div class="booking-date-section">
                    <i class="fas fa-calendar"></i>
                    <span>Created: ${formatDate(booking.created_at)}</span>
                  </div>
                </div>

                <!-- Card Body -->
                <div class="booking-card-body-enhanced">
                  <!-- Booking Information Section -->
                  <div class="info-section">
                    <div class="section-header">
                      <i class="fas fa-info-circle"></i>
                      <h6>Booking Information</h6>
                    </div>
                    <div class="info-grid">
                      <div class="info-item">
                        <span class="info-label">Package:</span>
                        <span class="info-value">${booking.package_name}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">Destination:</span>
                        <span class="info-value">${booking.destination}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Customer Information Section -->
                  <div class="info-section">
                    <div class="section-header">
                      <i class="fas fa-user"></i>
                      <h6>Customer Information</h6>
                    </div>
                    <div class="info-grid">
                      <div class="info-item">
                        <span class="info-label">Name:</span>
                        <span class="info-value">${booking.customer_name}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">Phone:</span>
                        <span class="info-value">${booking.customer_phone}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">Email:</span>
                        <span class="info-value">${booking.customer_email}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">Payment Method:</span>
                        <span class="info-value">${booking.payment_method || 'Card'}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Payment Details Section -->
                  <div class="info-section">
                    <div class="section-header">
                      <i class="fas fa-credit-card"></i>
                      <h6>Payment Details</h6>
                    </div>
                    <div class="info-grid">
                      <div class="info-item">
                        <span class="info-label">Advance Payment:</span>
                        <span class="info-value amount">${formatCurrency(booking.advance_amount)}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">Monthly EMI:</span>
                        <span class="info-value amount">${formatCurrency(booking.monthly_emi_amount)}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">Total EMI Amount:</span>
                        <span class="info-value amount">${formatCurrency(booking.total_emi_amount)}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Payment Status Section (Dropdown Style) -->
                  <div class="info-section payment-status-section">
                    <div class="section-header clickable" onclick="togglePaymentStatus('${booking.reference_id}')">
                      <i class="fas fa-chart-line"></i>
                      <h6>Payment Status</h6>
                      <i class="fas fa-chevron-down toggle-icon" id="toggle-${booking.reference_id}"></i>
                    </div>
                    <div class="payment-status-dropdown" id="status-${booking.reference_id}">
                      <div class="payment-progress-enhanced">
                        <div class="progress-info">
                          <div class="progress-item">
                            <div class="progress-icon paid">
                              <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="progress-details">
                              <span class="progress-label">Amount Paid</span>
                              <span class="progress-amount">${formatCurrency(booking.total_paid)}</span>
                            </div>
                          </div>
                          <div class="progress-item">
                            <div class="progress-icon pending">
                              <i class="fas fa-clock"></i>
                            </div>
                            <div class="progress-details">
                              <span class="progress-label">Amount Pending</span>
                              <span class="progress-amount">${formatCurrency(booking.estimated_total_cost - booking.total_paid)}</span>
                            </div>
                          </div>
                          <div class="progress-item">
                            <div class="progress-icon months-paid">
                              <i class="fas fa-check-double"></i>
                            </div>
                            <div class="progress-details">
                              <span class="progress-label">Months Paid</span>
                              <span class="progress-amount">${booking.paid_months || 1} of ${booking.total_months || 6} months</span>
                            </div>
                          </div>
                          <div class="progress-item">
                            <div class="progress-icon months-remaining">
                              <i class="fas fa-hourglass-half"></i>
                            </div>
                            <div class="progress-details">
                              <span class="progress-label">Months Remaining</span>
                              <span class="progress-amount">${booking.remaining_months || 5} months</span>
                            </div>
                          </div>
                          ${booking.next_due_date ? `
                            <div class="progress-item">
                              <div class="progress-icon due">
                                <i class="fas fa-calendar-alt"></i>
                              </div>
                              <div class="progress-details">
                                <span class="progress-label">Next Due Date</span>
                                <span class="progress-amount">${formatDate(booking.next_due_date)}</span>
                              </div>
                            </div>
                          ` : ''}
                        </div>
                        <div class="progress-bar-container">
                          <div class="progress-bar-enhanced">
                            <div class="progress-fill-enhanced" style="width: ${(booking.total_paid / booking.estimated_total_cost) * 100}%"></div>
                          </div>
                          <div class="progress-percentage">
                            ${Math.round((booking.total_paid / booking.estimated_total_cost) * 100)}% Completed
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="booking-actions">
                  ${booking.status === 'active' ? `
                    <button class="action-btn primary" onclick="makePayment('${booking.reference_id}')">
                      <i class="fas fa-credit-card"></i> Make Payment
                    </button>
                  ` : ''}
                </div>
              </div>
            `;
          }

          // Update booking modal with error
          function updateBookingModalWithError() {
            const modal = document.getElementById('bookingModal');
            if (!modal) return;

            const modalBody = modal.querySelector('.modal-body');
            modalBody.innerHTML = `
              <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h4>Unable to Load Bookings</h4>
                <p>We couldn't fetch your booking information. Please check your connection and try again.</p>
                <button class="retry-btn" onclick="retryLoadBookings()">
                  <i class="fas fa-redo"></i> Retry
                </button>
              </div>
            `;
          }

          // Retry loading bookings
          async function retryLoadBookings() {
            const modal = document.getElementById('bookingModal');
            if (!modal) return;

            const modalBody = modal.querySelector('.modal-body');
            modalBody.innerHTML = `
              <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>Loading your bookings...</p>
              </div>
            `;

            try {
              const bookings = await fetchUserBookings();
              updateBookingModal(bookings);
            } catch (error) {
              console.error('Error retrying bookings:', error);
              updateBookingModalWithError();
            }
          }

          // Toggle payment status dropdown
          function togglePaymentStatus(referenceId) {
            const dropdown = document.getElementById(`status-${referenceId}`);
            const toggleIcon = document.getElementById(`toggle-${referenceId}`);

            if (dropdown && toggleIcon) {
              const isOpen = dropdown.classList.contains('open');

              if (isOpen) {
                dropdown.classList.remove('open');
                toggleIcon.classList.remove('rotated');
              } else {
                dropdown.classList.add('open');
                toggleIcon.classList.add('rotated');
              }
            }
          }

          // Make payment for booking
          function makePayment(referenceId) {
            notificationManager.show(`Redirecting to payment for booking ${referenceId}`, 'info');
            // Here you would typically redirect to payment gateway or open payment modal
          }

          // Make booking history function globally accessible
          window.showBookingHistory = showBookingHistory;

          // Make other functions globally accessible for the user menu
          window.closeProfileModal = closeProfileModal;
          window.closeBookingModal = closeBookingModal;
          window.handleProfileUpdate = handleProfileUpdate;
          window.makePayment = makePayment;
          window.togglePaymentStatus = togglePaymentStatus;
          window.retryLoadBookings = retryLoadBookings;

          // Close booking modal
          function closeBookingModal() {
            const modal = document.getElementById('bookingModal');
            if (modal) {
              modal.remove();
            }
          }

          // Scroll to packages section
          function scrollToPackages() {
            const packagesSection = document.getElementById('resultsSection');
            if (packagesSection) {
              packagesSection.scrollIntoView({ behavior: 'smooth' });
            }
          }

          // Download package card as image
          function downloadPackageCard() {
            if (!window.currentCardData || !window.currentPackageData) {
              notificationManager.show('No package data available.', 'error');
              return;
            }

            try {
              // Get the card element
              const cardElement = document.getElementById('packagePreviewCard');
              if (!cardElement) {
                notificationManager.show('Card element not found.', 'error');
                return;
              }

              // Use html2canvas to convert the card to an image
              if (typeof html2canvas !== 'undefined') {
                html2canvas(cardElement, {
                  scale: 2,
                  useCORS: true,
                  allowTaint: true,
                  backgroundColor: null
                }).then(canvas => {
                  // Create download link
                  const link = document.createElement('a');
                  link.download = `${window.currentCardData.destination.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-package-card.png`;
                  link.href = canvas.toDataURL('image/png', 1.0);
                  link.click();
                  notificationManager.show('Package card downloaded successfully!', 'success');
                }).catch(error => {
                  console.error('Error generating card image:', error);
                  notificationManager.show('Error downloading card. Please try again.', 'error');
                });
              } else {
                notificationManager.show('Image generation not available. Please refresh the page.', 'error');
              }
            } catch (error) {
              console.error('Error downloading package card:', error);
              notificationManager.show('Error downloading package card. Please try again.', 'error');
            }
          }

          // Share package card
          async function sharePackageCard() {
            if (!window.currentCardData || !window.currentPackageData) {
              notificationManager.show('No package data available.', 'error');
              return;
            }

            try {
              // Get the card element
              const cardElement = document.getElementById('packagePreviewCard');
              if (!cardElement) {
                notificationManager.show('Card element not found.', 'error');
                return;
              }

              // Use html2canvas to convert the card to an image
              if (typeof html2canvas !== 'undefined') {
                html2canvas(cardElement, {
                  scale: 2,
                  useCORS: true,
                  allowTaint: true,
                  backgroundColor: null
                }).then(async canvas => {
                  // Convert canvas to blob
                  canvas.toBlob(async (blob) => {
                    const file = new File([blob], `${window.currentCardData.destination.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-package-card.png`, { type: 'image/png' });
                    
                    // Check if Web Share API is available
                    if (navigator.share && navigator.canShare) {
                      try {
                        if (navigator.canShare({ files: [file] })) {
                          await navigator.share({
                            title: `${window.currentCardData.destination} Family Package`,
                            text: `Check out this amazing ${window.currentCardData.destination} package for ₹${window.currentCardData.price.toLocaleString()}!`,
                            files: [file]
                          });
                          notificationManager.show('Package card shared successfully!', 'success');
                        } else {
                          // Fallback to download if sharing files is not supported
                          downloadPackageCard();
                        }
                      } catch (error) {
                        console.log('Error sharing:', error);
                        // Fallback to download
                        downloadPackageCard();
                      }
                    } else {
                      // Fallback to download if Web Share API is not available
                      downloadPackageCard();
                    }
                  }, 'image/png', 1.0);
                }).catch(error => {
                  console.error('Error generating card image:', error);
                  notificationManager.show('Error sharing card. Please try again.', 'error');
                });
              } else {
                notificationManager.show('Image generation not available. Please refresh the page.', 'error');
              }
            } catch (error) {
              console.error('Error sharing package card:', error);
              notificationManager.show('Error sharing package card. Please try again.', 'error');
            }
          }

          // Print Package PDF function
          async function printPackagePDF(mode) {
            if (!window.currentCardData || !window.currentPackageData) {
              notificationManager.show('No package data available.', 'error');
              return;
            }

            const cardData = window.currentCardData;
            const pkg = window.currentPackageData;

            notificationManager.show('Generating PDF preview...', 'info');

            // Use window.jspdf.jsPDF for UMD build
            const jsPDF = window.jspdf && window.jspdf.jsPDF;
            if (!jsPDF) {
              notificationManager.show('PDF generation not available. Please refresh the page.', 'error');
              return;
            }

            // Optimized: fetch, down-scale and compress image to reduce PDF size
            async function getImageDataUrl(url, options = {}) {
              try {
                const response = await fetch(url, { mode: 'cors' });
                const blob = await response.blob();
                return await new Promise((resolve, reject) => {
                  const img = new Image();
                  img.crossOrigin = 'anonymous';
                  img.onload = () => {
                    // Down-scale very large images to a reasonable width before embedding
                    const MAX_WIDTH = 800; // px – good balance of quality vs. size
                    const scale = Math.min(1, MAX_WIDTH / img.width);
                    const canvas = document.createElement('canvas');
                    canvas.width = img.width * scale;
                    canvas.height = img.height * scale;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                    // Export as JPEG at 0.7 quality for further compression
                    resolve(canvas.toDataURL('image/jpeg', 0.7));
                  };
                  img.onerror = reject;
                  img.src = URL.createObjectURL(blob);
                });
              } catch (e) {
                return null;
              }
            }

            try {
              const doc = new jsPDF('p', 'mm', 'a4');
              const pageWidth = doc.internal.pageSize.getWidth();
              const pageHeight = doc.internal.pageSize.getHeight();
              const margin = 14; // smaller margin for more content
              const contentWidth = pageWidth - (margin * 2);
              let yPosition = 50; // move header and main image down by 13cm (130mm)

              // --- Add a colored accent bar at the top of the first page ---
              doc.setFillColor(21, 171, 139); // #15ab8b
              doc.rect(0, 0, pageWidth, 6, 'F'); // Move this bar to the very top, above all content (logo/header)

              // --- Load PDF background images ---
              const bgPage1Url = 'img/tripxplo-pdf-bg-page-1.jpg';
              // Choose second page background based on EMI plan
              let bgPage2Url = 'img/tripxplo-pdf-bg-page-3.jpg';
              const selectedEmiMonths = cardData.emi.months || pkg.emi_options?.find(e=>e.selected)?.months || 6;

              if (selectedEmiMonths >= 12) {
                bgPage2Url = 'img/tripxplo-pdf-bg-page-2.jpg';
              } else if (selectedEmiMonths >= 6) {
                bgPage2Url = 'img/tripxplo-pdf-bg-page-3.jpg';
              } else {
                bgPage2Url = 'img/tripxplo-pdf-bg-page-4.jpg';
              }
              const bgPage1DataUrl = await getImageDataUrl(bgPage1Url);
              const bgPage2DataUrl = await getImageDataUrl(bgPage2Url);

              // Helper to draw background for current page
              function drawBackground(pageNum) {
                if (pageNum === 1 && bgPage1DataUrl) {
                  doc.addImage(bgPage1DataUrl, 'JPEG', 0, 0, pageWidth, pageHeight);
                } else if (pageNum === 2 && bgPage2DataUrl) {
                  doc.addImage(bgPage2DataUrl, 'JPEG', 0, 0, pageWidth, pageHeight);
                } else if (bgPage2DataUrl) {
                  doc.addImage(bgPage2DataUrl, 'JPEG', 0, 0, pageWidth, pageHeight);
                }
              }

              // Draw background for first page
              drawBackground(1);
              let currentPage = 1;

              // --- Family Prepaid EMI Package Heading ---
              doc.setFontSize(15);
              doc.setFont('helvetica', 'bold');
              doc.setTextColor(21, 171, 139);
              doc.text('Family Prepaid EMI Package', pageWidth / 2, yPosition, { align: 'center' });
              yPosition += 12;

              // --- Package Title & Details ---
              yPosition += 5.5;
              doc.setFontSize(16);
              doc.setFont('helvetica', 'bold');
              doc.setTextColor(21, 171, 139);
              // Improved split logic for package title
              let mainTitle = pkg.quote_name || pkg.package_name || pkg.title || `${cardData.destination} Travel Package`;
              let line1 = mainTitle;
              let line2 = '';
              // Try to split at colon, hyphen, or comma
              let splitMatch = mainTitle.match(/(.+?)[\-:,](.+)/);
              if (splitMatch) {
                line1 = splitMatch[1].trim();
                line2 = splitMatch[2].trim();
              } else if ((pkg.destination || cardData.destination) && mainTitle.toLowerCase().includes('in ' + (pkg.destination || cardData.destination).toLowerCase())) {
                // Split at 'in [Destination]'
                let dest = (pkg.destination || cardData.destination);
                let idx = mainTitle.toLowerCase().indexOf('in ' + dest.toLowerCase());
                if (idx !== -1) {
                  line1 = mainTitle.slice(0, idx + 2).trim();
                  line2 = mainTitle.slice(idx + 2).trim();
                }
              } else if ((pkg.destination || cardData.destination) && mainTitle !== (pkg.destination || cardData.destination)) {
                line2 = pkg.destination || cardData.destination;
              }
              doc.text(line1, pageWidth / 2, yPosition, { align: 'center' });
              yPosition += 8.5;
              if (line2) {
                doc.setFontSize(16);
                doc.setFont('helvetica', 'bold');
                doc.setTextColor(21, 171, 139);
                doc.text(line2, pageWidth / 2, yPosition, { align: 'center' });
              yPosition += 7.5;
              }
              doc.setFontSize(13);
              doc.setFont('helvetica', 'normal');
              doc.setTextColor(40, 40, 40);
              const labelColor = [21, 171, 139];
              const valueColor = [40, 40, 40];
              const labelFont = { font: 'helvetica', style: 'bold' };
              const valueFont = { font: 'helvetica', style: 'normal' };
              const labelOffset = 10;
              let detailsRowY = yPosition + 2;
              // Destination
              doc.setFont(labelFont.font, labelFont.style);
              doc.setTextColor(...labelColor);
              doc.text('Destination:', margin + labelOffset, detailsRowY);
              doc.setFont(valueFont.font, valueFont.style);
              doc.setTextColor(...valueColor);
              doc.text(`${pkg.destination || cardData.destination}`, margin + 32 + labelOffset, detailsRowY);
              detailsRowY += 8.5;
              // Duration (robust display)
              doc.setFont(labelFont.font, labelFont.style);
              doc.setTextColor(...labelColor);
              doc.text('Duration:', margin + labelOffset, detailsRowY);
              doc.setFont(valueFont.font, valueFont.style);
              doc.setTextColor(...valueColor);
              let durationText = '';
              try {
                // Try to get duration from the overview tab in the DOM
                const overviewDuration = document.querySelector('.package-summary-card .detail-row .detail-item span strong')?.nextSibling?.textContent?.trim();
                if (overviewDuration) {
                  durationText = overviewDuration;
                } else if (pkg.nights && pkg.duration_days) {
                  durationText = `${pkg.nights}N/${pkg.duration_days}D`;
                } else if (pkg.nights) {
                  durationText = `${pkg.nights}N`;
                } else if (pkg.duration_days) {
                  durationText = `${pkg.duration_days}D`;
                } else {
                  durationText = '';
                }
              } catch (e) {
                if (pkg.nights && pkg.duration_days) {
                  durationText = `${pkg.nights}N/${pkg.duration_days}D`;
                } else if (pkg.nights) {
                  durationText = `${pkg.nights}N`;
                } else if (pkg.duration_days) {
                  durationText = `${pkg.duration_days}D`;
                } else {
                  durationText = '';
                }
              }
              doc.text(durationText, margin + 32 + labelOffset, detailsRowY);
              detailsRowY += 8.5;
              // Family Type
              doc.setFont(labelFont.font, labelFont.style);
              doc.setTextColor(...labelColor);
              doc.text('Family Type:', margin + labelOffset, detailsRowY);
              doc.setFont(valueFont.font, valueFont.style);
              doc.setTextColor(...valueColor);
              doc.text(`${pkg.family_type || (window.detectFamilyType && detectFamilyType().family_type) || 'Family'}`, margin + 32 + labelOffset, detailsRowY);
              detailsRowY += 8.5;
              // Total Price
              let price = (pkg.total_price || 0).toLocaleString();
              price = price.normalize('NFKD').replace(/[^0-9\d₹,]+/g, '');
              if (!price.startsWith('₹')) price = '₹' + price.replace(/^₹+/, '');
              price = price.replace('₹', 'INR ');
              doc.setFont(labelFont.font, labelFont.style);
              doc.setTextColor(...labelColor);
              doc.text('Total Price:', margin + labelOffset, detailsRowY);
              doc.setFont(valueFont.font, valueFont.style);
              doc.setTextColor(...valueColor);
              doc.text(`${price}`.trim(), margin + 32 + labelOffset, detailsRowY);
              detailsRowY += 8.5;
              // EMI Plan
              let emiPriceString = ('₹' + cardData.price.toLocaleString())
                .normalize('NFKD')
                .replace(/[^0-9₹,]+/g, '')
                .replace(/^₹+/, '₹');
              emiPriceString = emiPriceString.replace('₹', 'INR ');
              doc.setFont(labelFont.font, labelFont.style);
              doc.setTextColor(...labelColor);
              doc.text('EMI Plan:', margin + labelOffset, detailsRowY);
              doc.setFont(valueFont.font, valueFont.style);
              doc.setTextColor(...valueColor);
              const emiPlanText = isCustomEmi ?
                `Custom ${cardData.emi.months} months × ${emiPriceString}` :
                `${cardData.emi.months} months × ${emiPriceString}`;
              doc.text(emiPlanText.trim(), margin + 32 + labelOffset, detailsRowY);
              yPosition = detailsRowY + 8;
              doc.setFont('helvetica', 'normal');
              doc.setFontSize(11);
              doc.setTextColor(60, 60, 60);

              // --- Overview Section ---
              // Draw colored box for section header (Package Overview, same as EMI Payment Schedule)
              const overviewHeaderBoxHeight = 9;
              // Calculate total number of lines for all inclusions/exclusions
              const sanitizeList = (list, fallback) => {
                const cleaned = (Array.isArray(list) ? list : [])
                  .map(item => (item || '').toString().trim())
                  .filter(Boolean);
                return (cleaned.length ? cleaned : fallback).slice(0, 5);
              };
              const inclusions = sanitizeList(pkg.inclusions, ['Accommodation', 'Transfers', 'Sight-seeing']);
              let domExclusions = [];
              try {
                const exclusionNodes = document.querySelectorAll('.package-exclusions .exclusion-item span');
                domExclusions = Array.from(exclusionNodes).map(node => node.textContent.trim()).filter(Boolean);
              } catch (e) { domExclusions = []; }
              const exclusions = sanitizeList(domExclusions.length ? domExclusions : pkg.exclusions, ['Personal expenses', 'Travel insurance', 'Items not mentioned in inclusions']);
              let hotelInclusions = [];
              if (pkg.hotels_list && pkg.hotels_list.length > 0) {
                hotelInclusions = pkg.hotels_list.map(hotel => {
                  const nights = hotel.nights || hotel.stay_nights || 1;
                  const hotelName = hotel.hotel_name || 'Hotel Included';
                  let mealPlan = hotel.meal_plan || 'Breakfast included';
                  mealPlan = getMealPlanDescription(mealPlan);
                  return `${nights}N - ${hotelName} (${mealPlan})`;
                });
              } else if (pkg.hotel_name) {
                let mealPlan = getMealPlanDescription(pkg.hotel_category);
                hotelInclusions = [`${pkg.hotel_name} (${mealPlan})`];
              }
              const allInclusions = hotelInclusions.concat(
                inclusions.filter(
                  inc => !hotelInclusions.some(hotelInc => {
                    const codeMatch = inc.match(/\((CP|MAP|AP|EP)\)$/i);
                    if (codeMatch) return true;
                    return hotelInc.includes(inc.replace(/\((CP|MAP|AP|EP)\)$/i, '').trim());
                  })
                )
              );
              const colWidth = (contentWidth - 8) / 2;
              let maxRows = Math.max(allInclusions.length, exclusions.length);
              // Calculate total number of lines for all inclusions/exclusions
              let totalIncExcLines = 0;
              for (let i = 0; i < maxRows; i++) {
                let inclusionLines = allInclusions[i] ? doc.splitTextToSize(allInclusions[i], colWidth - 10) : [];
                let exclusionLines = exclusions[i] ? doc.splitTextToSize(exclusions[i], colWidth - 10) : [];
                totalIncExcLines += Math.max(inclusionLines.length, exclusionLines.length, 1);
              }
              // --- Draw background box for entire Package Overview section ---
              let overviewBoxY = yPosition;
              let overviewBoxHeight = overviewHeaderBoxHeight + 2 + 6 + 4 + (totalIncExcLines * 7.5) + 8; // header + gap + subheaders + gap + data + padding
              doc.setFillColor(206, 241, 236);
              doc.roundedRect(margin - 2, overviewBoxY - 2, contentWidth + 4, overviewBoxHeight, 6, 6, 'F');
              // Now draw the section header and content on top of the box
              doc.setFillColor(21, 171, 139);
              doc.roundedRect(margin, yPosition, contentWidth, overviewHeaderBoxHeight, 4, 4, 'F');
              doc.setTextColor(255, 255, 255);
              doc.setFontSize(15);
              doc.setFont('helvetica', 'bold');
              doc.text('Package Overview', margin + 4, yPosition + overviewHeaderBoxHeight/2 + 2.5);
              yPosition += overviewHeaderBoxHeight + 2;
              yPosition += 6; 
              doc.setTextColor(21, 171, 139);
              doc.setFontSize(12);
              doc.setFont('helvetica', 'bold');
              doc.text('What\'s Included:', margin, yPosition);
              doc.text('What\'s Not Included:', margin + colWidth + 10, yPosition);
              doc.setFont('helvetica', 'normal');
              yPosition += 4; 
              doc.setFontSize(11);
              let rowY = yPosition + 6;
              for (let i = 0; i < maxRows; i++) {
                let inclusionLines = allInclusions[i] ? doc.splitTextToSize(allInclusions[i], colWidth - 10) : [];
                let exclusionLines = exclusions[i] ? doc.splitTextToSize(exclusions[i], colWidth - 10) : [];
                let maxLines = Math.max(inclusionLines.length, exclusionLines.length, 1);
                for (let l = 0; l < maxLines; l++) {
                  if (inclusionLines[l]) {
                    doc.setTextColor(21, 171, 139);
                    if (l === 0) doc.text('•', margin + 2, rowY);
                    doc.setTextColor(40, 40, 40);
                    doc.text(inclusionLines[l], margin + 8, rowY);
                  }
                  if (exclusionLines[l]) {
                    doc.setTextColor(241, 90, 43);
                    if (l === 0) doc.text('•', margin + colWidth + 10, rowY);
                    doc.setTextColor(40, 40, 40);
                    doc.text(exclusionLines[l], margin + colWidth + 16, rowY);
                  }
                  rowY += 7.5;
                }
              }
              yPosition = rowY + 12; // add extra gap after the box before itinerary section

              // --- Itinerary Section (multi-page robust) ---
              if (currentPage > 1) {
                yPosition = 30;
              }
              // --- Extract itinerary from DOM if available ---
              let domItinerary = [];
              try {
                const timeline = document.querySelector('.itinerary-timeline');
                if (timeline) {
                  const dayNodes = timeline.querySelectorAll('.day-item');
                  domItinerary = Array.from(dayNodes).map((node, idx) => {
                    const dayNumEl = node.querySelector('.day-number');
                    const dayNum = dayNumEl ? dayNumEl.textContent.trim() : (idx + 1);
                    const titleEl = node.querySelector('.day-header h5');
                    const title = titleEl ? titleEl.textContent.trim() : '';
                    const descEl = node.querySelector('.day-description');
                    const description = descEl ? descEl.textContent.trim() : '';
                    return { day: dayNum, title, description };
                  });
                }
              } catch (e) { domItinerary = []; }
              // Use DOM itinerary if available, else pkg.itinerary
              const itineraryData = (domItinerary && domItinerary.length > 0) ? domItinerary : (pkg.itinerary || []);
              // Helper to draw itinerary section header and background box for the current page
              function drawItineraryHeaderAndBox(boxY, boxHeight, showHeader, isSubsequentPage) {
                let adjBoxY = boxY;
                if (isSubsequentPage) adjBoxY -= 8; // move up on 2nd+ pages
                doc.setFillColor(206, 241, 236); //rgb(206, 241, 236)
                doc.roundedRect(margin - 2, adjBoxY - 2, contentWidth + 4, boxHeight + (isSubsequentPage ? 8 : 0), 6, 6, 'F');
                if (showHeader) {
                doc.setFillColor(21, 171, 139);
                  doc.roundedRect(margin, boxY, contentWidth, 9, 4, 4, 'F');
                doc.setTextColor(255, 255, 255);
                  doc.setFontSize(14);
                doc.setFont('helvetica', 'bold');
                  doc.text('Detailed Itinerary', margin + 4, boxY + 9/2 + 2.5);
                }
                // Do not update yPosition here; extra space will be added to renderY in the loop
              }
              // Pre-calculate heights for each day
              let dayHeights = [];
              if (itineraryData && Array.isArray(itineraryData) && itineraryData.length > 0) {
                itineraryData.forEach((day, idx) => {
                  let h = 6.5; // day header
                  const detailsArr = [];
                  if (Array.isArray(day.highlights) && day.highlights.length) {
                    detailsArr.push(day.highlights.join(', '));
                  }
                  if (day.description) {
                    detailsArr.push(day.description);
                  }
                  const detailText = detailsArr.join(' | ');
                  if (detailText) {
                    const splitted = doc.splitTextToSize(detailText, contentWidth - 10);
                    h += splitted.length * 5.2;
                  }
                  if (idx < itineraryData.length - 1) {
                    h += 2.5;
                  }
                  h += 2.5;
                  dayHeights.push(h);
                });
              }
              // Render itinerary with page breaks, drawing background and header per page
              let lastDayBottom = yPosition;
              let itineraryHeaderBoxHeight = 9;
              let itineraryBoxPadding = 4;
              let i = 0;
              let isFirstItineraryPage = true;
              while (i < itineraryData.length) {
                let boxY = yPosition;
                let startY = yPosition + (isFirstItineraryPage ? itineraryHeaderBoxHeight + 2 + 2.5 : 0);
                let simY = startY;
                let daysThisPage = [];
                // Fit as many days as possible on this page
                for (; i < itineraryData.length; i++) {
                  let h = dayHeights[i];
                  if (simY + h > 285 && daysThisPage.length > 0) break;
                  daysThisPage.push(i);
                  simY += h;
                }
                // Calculate boxHeight as the actual content height for this page
                let boxHeight = (simY - boxY) + itineraryBoxPadding;
                // Only add header height on first page (already included in startY)
                // Do not add extra header height to boxHeight
                drawItineraryHeaderAndBox(boxY, boxHeight, isFirstItineraryPage, !isFirstItineraryPage);
                // Render the section header and content for this page
                let renderY = boxY + (isFirstItineraryPage ? itineraryHeaderBoxHeight + 2 + 2.5 + 7 : 0);
                let isFirstDayRendered = isFirstItineraryPage && daysThisPage[0] === 0;
                for (let localIdx = 0; localIdx < daysThisPage.length; localIdx++) {
                  const idx = daysThisPage[localIdx];
                  const day = itineraryData[idx];
                  doc.setFont('helvetica', 'bold');
                  doc.setFontSize(13);
                  doc.setTextColor(21, 171, 139);
                  doc.text(day.title ? day.title : `Day ${day.day ?? idx + 1}`, margin + 4, renderY);
                  renderY += 6.5;
                  doc.setFont('helvetica', 'normal');
                  doc.setFontSize(11);
                  doc.setTextColor(40, 40, 40);
                  const detailsArr = [];
                  if (Array.isArray(day.highlights) && day.highlights.length) {
                    detailsArr.push(day.highlights.join(', '));
                  }
                  if (day.description) {
                    detailsArr.push(day.description);
                  }
                  const detailText = detailsArr.join(' | ');
                  if (detailText) {
                    const splitted = doc.splitTextToSize(detailText, contentWidth - 10);
                    splitted.forEach(line => {
                      doc.setTextColor(21, 171, 139);
                      doc.text('•', margin + 7, renderY);
                      doc.setTextColor(40, 40, 40);
                      doc.text(line, margin + 13, renderY);
                      renderY += 5.2;
                    });
                  }
                  if (idx < itineraryData.length - 1) {
                    doc.setDrawColor(230, 248, 245);
                    doc.setLineWidth(1);
                    doc.line(margin + 4, renderY, margin + contentWidth - 4, renderY);
                    renderY += 2.5;
                  }
                  renderY += 2.5;
                  lastDayBottom = renderY;
                }
                // Prepare for next page if needed
                if (i < itineraryData.length) {
                  doc.addPage();
                  currentPage++;
                  drawBackground(currentPage);
                  yPosition = 30;
                  isFirstItineraryPage = false;
                } else {
                  yPosition = lastDayBottom + 2;
                }
              }
              if (!itineraryData || !Array.isArray(itineraryData) || itineraryData.length === 0) {
                doc.setFont('helvetica', 'italic');
                doc.setFontSize(11);
                doc.setTextColor(100, 100, 100);
                doc.text('Itinerary details will be provided upon booking confirmation.', margin, yPosition);
                yPosition += 5;
                doc.text('Our travel experts will create a personalized schedule based on your preferences.', margin, yPosition);
                yPosition += 5;
              }

              // --- EMI Schedule Section (Enhanced, full schedule, visible table) ---
              doc.setDrawColor(230, 230, 230);
              doc.setLineWidth(0.5);
              doc.line(margin, yPosition, margin + contentWidth, yPosition);
              yPosition += 2.5;
              // Draw colored box for section header (rounded)
              const emiHeaderBoxHeight = 9;
              doc.setFillColor(21, 171, 139);
              doc.roundedRect(margin, yPosition, contentWidth, emiHeaderBoxHeight, 4, 4, 'F');
              doc.setTextColor(255, 255, 255);
              doc.setFontSize(14);
              doc.setFont('helvetica', 'bold');
              doc.text('EMI Payment Schedule', margin + 4, yPosition + emiHeaderBoxHeight/2 + 2.5);
              yPosition += emiHeaderBoxHeight + 2;
              yPosition += 2.5;

              // Prepare EMI table data
              const selectedEmiOption = pkg.emi_options && pkg.emi_options.length > 0 ?
                pkg.emi_options.find(emi => emi.selected) || pkg.emi_options[0] : null;
              let months = selectedEmiOption ? (selectedEmiOption.months || cardData.emi.months) : cardData.emi.months;
              let emiAmount = selectedEmiOption ? (selectedEmiOption.monthly_amount || selectedEmiOption.emi_amount || cardData.price) : cardData.price;
              if (typeof emiAmount !== 'number') {
                emiAmount = Number((emiAmount || '').toString().replace(/[^\d.]/g, '')) || 0;
              }
              let processingFee = 0; // No processing fee for prepaid EMI

              // Handle custom EMI plans
              const isCustomEmi = selectedEmiOption && selectedEmiOption.is_custom;
              const emiLabel = isCustomEmi ? 'Custom Prepaid EMI' : 'Prepaid EMI';
              const today = new Date();
              // First payment (today) - no processing fee for prepaid EMI
              const firstPayment = emiAmount;
              let emiRows = [];
              emiRows.push([
                today.toLocaleDateString('en-IN', { day: '2-digit', month: 'short', year: 'numeric' }),
                `1st ${emiLabel}`,
                `INR ${String(firstPayment).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
              ]);
              for (let i = 2; i <= months; i++) {
                const paymentDate = new Date(today);
                paymentDate.setMonth(paymentDate.getMonth() + (i - 1));
                emiRows.push([
                  paymentDate.toLocaleDateString('en-IN', { day: '2-digit', month: 'short', year: 'numeric' }),
                  `${i}${getOrdinalSuffix(i)} ${emiLabel}`,
                  `INR ${String(emiAmount).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
                ]);
              }
              // Add total row
              const totalAmount = emiAmount * months; // No processing fee for prepaid EMI
              emiRows.push([
                '',
                'Total Package Cost',
                `INR ${String(totalAmount).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
              ]);

              // Custom table rendering (no autoTable)
              const tableCol1 = margin + 10;
              const tableCol2 = margin + 44;
              const tableCol3 = margin + contentWidth - 25;
              const rowHeight = 8;
              // Header row with background and visible text
              doc.setFillColor(21, 171, 139);
                  doc.rect(margin, yPosition, contentWidth, rowHeight, 'F');
              doc.setDrawColor(200, 200, 200);
              doc.setLineWidth(0.3);
              doc.rect(margin, yPosition, contentWidth, rowHeight, 'S');
                  doc.setFont('helvetica', 'bold');
              doc.setFontSize(11);
              doc.setTextColor(255, 255, 255);
              doc.text('Date', tableCol1 + 8, yPosition + 5, { align: 'center' });
              doc.text('Description', tableCol2, yPosition + 5);
              doc.text('Amount', tableCol3, yPosition + 5, { align: 'right' });
              // Draw vertical column lines for header
              doc.line(tableCol2 - 8, yPosition, tableCol2 - 8, yPosition + rowHeight * (emiRows.length + 1));
              doc.line(tableCol3 - 20, yPosition, tableCol3 - 20, yPosition + rowHeight * (emiRows.length + 1));
                  yPosition += rowHeight;
              // Table rows
              doc.setFont('helvetica', 'normal');
              doc.setFontSize(10);
              for (let i = 0; i < emiRows.length; i++) {
                // Alternating row backgrounds
                if (i % 2 === 0) {
                  doc.setFillColor(230, 250, 245);
                } else {
                  doc.setFillColor(245, 255, 250);
                }
                  doc.rect(margin, yPosition, contentWidth, rowHeight, 'F');
                // Row borders
                doc.setDrawColor(200, 200, 200);
                doc.setLineWidth(0.3);
                doc.rect(margin, yPosition, contentWidth, rowHeight, 'S');
                // Text
                doc.setFont('helvetica', i === emiRows.length - 1 ? 'bold' : 'normal');
                doc.setFontSize(i === emiRows.length - 1 ? 11 : 10);
                doc.setTextColor(i === emiRows.length - 1 ? 21 : 60, i === emiRows.length - 1 ? 171 : 60, i === emiRows.length - 1 ? 139 : 60);
                doc.text(emiRows[i][0], tableCol1 + 8, yPosition + 5, { align: 'center' });
                doc.text(emiRows[i][1], tableCol2, yPosition + 5);
                if (i === emiRows.length - 1) {
                  doc.text(emiRows[i][2], tableCol3 + 4, yPosition + 5, { align: 'right' });
                } else {
                  doc.text(emiRows[i][2], tableCol3, yPosition + 5, { align: 'right' });
                }
                yPosition += rowHeight;
              }
              // Table outer border
              doc.setDrawColor(200, 200, 200);
              doc.setLineWidth(0.3);
              doc.rect(margin, yPosition - rowHeight * (emiRows.length + 1), contentWidth, rowHeight * (emiRows.length + 1), 'S');
              // Draw vertical column lines for the whole table
              doc.line(tableCol2 - 8, yPosition - rowHeight * (emiRows.length + 1), tableCol2 - 8, yPosition);
              doc.line(tableCol3 - 20, yPosition - rowHeight * (emiRows.length + 1), tableCol3 - 20, yPosition);
              yPosition += 10;

              // Add General Terms & Conditions above FINAL PRICE
              const termsHeader = 'General Terms & Conditions';
              const terms = [
                '• Prices are subject to change without prior notice',
                '• Hotel check-in/check-out times are as per hotel policy',
                '• Rates are valid for the mentioned dates only'
              ];
              doc.setFont('helvetica', 'bold');
              doc.setFontSize(12);
              doc.setTextColor(21, 171, 139);
              doc.text(termsHeader, margin, yPosition);
              yPosition += 6;
              doc.setFont('helvetica', 'normal');
              doc.setFontSize(11);
              doc.setTextColor(80, 80, 80);
              terms.forEach(term => {
                doc.text(term, margin + 2, yPosition);
                yPosition += 5;
              });
              yPosition += 4;

              // After EMI Payment Schedule table, add FINAL PRICE box
              const finalPrice = `INR ${String(totalAmount).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
              const finalPriceText = `FINAL PRICE (Incl. GST) -                          ${finalPrice}`;
              const boxHeight = 16;
              // Main box
              doc.setFillColor(21, 171, 139); // var(--primary)
              doc.roundedRect(margin, yPosition, contentWidth, boxHeight, 4, 4, 'F');
              doc.setFont('helvetica', 'bold');
              doc.setFontSize(14);
              // Main text: white, on top
              doc.setTextColor(255, 255, 255);
              doc.text(finalPriceText, margin + contentWidth / 2, yPosition + boxHeight / 2 + 0.5, { align: 'center', baseline: 'middle' });
              doc.setTextColor(40, 40, 40);
              yPosition += boxHeight + 6;

              const fileName = `${cardData.destination.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-package-details.pdf`;
              if (mode === 'preview') {
                const pdfBlob = doc.output('blob');
                const pdfUrl = URL.createObjectURL(pdfBlob);
                window.open(pdfUrl, '_blank');
                notificationManager.show('PDF preview opened in new tab!', 'success');
              } else {
                doc.save(fileName);
                notificationManager.show('PDF generated successfully!', 'success');
              }
            } catch (error) {
              console.error('Error creating PDF:', error);
              notificationManager.show('Error creating PDF. Please try again.', 'error');
            }

            // Helper for ordinal suffix
            function getOrdinalSuffix(num) {
              const j = num % 10, k = num % 100;
              if (j == 1 && k != 11) return 'st';
              if (j == 2 && k != 12) return 'nd';
              if (j == 3 && k != 13) return 'rd';
              return 'th';
            }

            // Helper to convert meal plan codes to descriptions (from HTML selection)
            function getMealPlanDescription(mealPlan) {
              if (!mealPlan) return 'Breakfast included';
              const code = mealPlan.toUpperCase();
              if (code === 'CP') return 'Breakfast included';
              if (code === 'MAP') return 'Breakfast & Dinner included';
              if (code === 'AP') return 'All meals included';
              if (code === 'EP') return 'No meals included';
              return mealPlan;
            }
          }

          // Make functions globally available
          window.printPackagePDF = printPackagePDF;
          window.downloadPackageCard = downloadPackageCard;
          window.sharePackageCard = sharePackageCard;
        </script>

        <!-- Footer -->
        <footer class="footer-section">
          <div class="footer-container">
            <div class="footer-content">
              <div class="footer-brand">
                <div class="footer-logo">
                  <span class="logo-trip">TRIP</span><span class="logo-xplo">XPLO</span>
                </div>
                <p class="footer-tagline">Make your dream vacation affordable with our monthly prepaid EMI plans</p>
              </div>

              <div class="footer-links">
                <div class="footer-column">
                  <h4>Company</h4>
                  <a href="#about">About</a>
                  <a href="#careers">Careers</a>
                  <a href="#press">Press</a>
                </div>

                <div class="footer-column">
                  <h4>Support</h4>
                  <a href="#contact">Contact</a>
                  <a href="#help">Help/FAQ</a>
                  <a href="#support">Support</a>
                </div>

                <div class="footer-column">
                  <h4>Legal</h4>
                  <a href="#terms">Terms of Service</a>
                  <a href="#privacy">Privacy Policy</a>
                  <a href="#cancellation">Cancellation Policy</a>
                </div>
              </div>
            </div>

            <div class="footer-bottom">
              <div class="footer-address">
                <h5>Registered Office:</h5>
                <p>Najam Centre, 2nd Floor, 29/108, Old Bridge Road, Near Vishnu Cinemas, Viruthampet, Vellore, 632006</p>
              </div>
              <div class="footer-social">
                <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
              </div>
            </div>

            <div class="footer-copyright">
              <p>© 2025 TripXplo. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  </div>
</div>
</body>
</html>